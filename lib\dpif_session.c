#include "dpif_private.h"
#include <arpa/inet.h>
#include <rte_errno.h>
#include <rte_mbuf.h>
#include <rte_mempool.h>
#include <stdio.h>

extern dpif_global_context_t *g_dpif_ctx;

/**
 * @brief Converts an IPv4 address (Network Byte Order) to an IPv4-mapped IPv6 address (Network Byte Order).
 *
 * @param ipv4_addr_nbo The IPv4 address in NBO.
 * @param ipv6_addr_out_nbo Output buffer (16 bytes) to store the IPv4-mapped IPv6 address in NBO.
 *                          The buffer must be pre-allocated by the caller.
 */
static void ipv4_nbo_to_ipv4_mapped_ipv6_nbo(uint32_t ipv4_addr_nbo, uint8_t ipv6_addr_out_nbo[16]) {
    memset(ipv6_addr_out_nbo, 0, 10);  // First 80 bits are zero
    ipv6_addr_out_nbo[10] = 0xff;
    ipv6_addr_out_nbo[11] = 0xff;
    // The ipv4_addr_nbo is already in network byte order.
    // memcpy will copy its bytes in that order.
    memcpy(&ipv6_addr_out_nbo[12], &ipv4_addr_nbo, sizeof(uint32_t));
}

/**
 * @brief Creates a canonical flow key from parsed flow information.
 *
 * The canonical key ensures that a flow is always represented the same way,
 * regardless of the original packet's direction (e.g., by using lesser/greater IPs and ports).
 * @param flow_info Pointer to the parsed flow information.
 * @param key Pointer to the dpi_flow_key_t structure to be filled.
 * @return 0 on success, negative errno on failure.
 */
int canonicalize_flow_key(const struct dpif_flow_info *flow_info, dpi_flow_key_t *key) {
    if (!flow_info || !key) {
        DPIF_LOG_ERROR("canonicalize_flow_key: Invalid arguments.");
        return -EINVAL;
    }

    memset(key, 0, sizeof(dpi_flow_key_t));  // Initialize key to zero
    key->address_family = flow_info->address_family;
    key->proto = flow_info->proto;

    uint8_t ip_src_nbo[16];
    uint8_t ip_dst_nbo[16];

    if (flow_info->address_family == AF_INET) {
        ipv4_nbo_to_ipv4_mapped_ipv6_nbo(flow_info->src_ip_u.ipv4_src_ip, ip_src_nbo);
        ipv4_nbo_to_ipv4_mapped_ipv6_nbo(flow_info->dst_ip_u.ipv4_dst_ip, ip_dst_nbo);
    } else if (flow_info->address_family == AF_INET6) {
        memcpy(ip_src_nbo, flow_info->src_ip_u.ipv6_src_ip, 16);
        memcpy(ip_dst_nbo, flow_info->dst_ip_u.ipv6_dst_ip, 16);
    } else {
        DPIF_LOG_ERROR("canonicalize_flow_key: Unknown address family: %u", flow_info->address_family);
        return -EINVAL;  // Or handle as an error appropriately
    }

    // Ports from flow_info are NBO, convert to HBO for comparison
    uint16_t src_port_hbo = rte_be_to_cpu_16(flow_info->src_port);
    uint16_t dst_port_hbo = rte_be_to_cpu_16(flow_info->dst_port);

    // Compare IPs (which are now effectively 16-byte NBO arrays)
    int ip_cmp = memcmp(ip_src_nbo, ip_dst_nbo, 16);

    if (ip_cmp < 0 || (ip_cmp == 0 && src_port_hbo < dst_port_hbo)) {
        memcpy(key->ip_lesser, ip_src_nbo, 16);
        key->port_lesser = src_port_hbo;  // Store as HBO
        memcpy(key->ip_greater, ip_dst_nbo, 16);
        key->port_greater = dst_port_hbo;  // Store as HBO
    } else if (ip_cmp > 0 || (ip_cmp == 0 && src_port_hbo > dst_port_hbo)) {
        memcpy(key->ip_lesser, ip_dst_nbo, 16);
        key->port_lesser = dst_port_hbo;  // Store as HBO
        memcpy(key->ip_greater, ip_src_nbo, 16);
        key->port_greater = src_port_hbo;        // Store as HBO
    } else {                                     // IPs and ports are identical
        memcpy(key->ip_lesser, ip_src_nbo, 16);  // Or dst, doesn't matter
        key->port_lesser = src_port_hbo;
        memcpy(key->ip_greater, ip_dst_nbo, 16);
        key->port_greater = dst_port_hbo;
    }
    // Padding is already zeroed by the initial memset of key.
    return 0;
}

/**
 * @brief Callback function executed periodically by each RX thread's timer
 * to call the application's dpi_session_update for its managed sessions.
 *
 * @param timer Pointer to the timer that expired (unused).
 * @param arg   Argument passed to rte_timer_reset, which is the dpif_rx_context_t.
 */
void dpif_session_handle_periodic_updates(struct rte_timer *timer __rte_unused, void *arg) {
    dpif_rx_context_t *ctx = (dpif_rx_context_t *) arg;

    if (!ctx || !ctx->session_table || !ctx->registered_callbacks || !ctx->registered_callbacks->dpi_session_update) {
        DPIF_LOG_WARNING("RX Lcore %u: Periodic session update callback skipped due to invalid context or missing "
                         "update "
                         "handler.",
                         rte_lcore_id());  // Use rte_lcore_id() as ctx->lcore_id might not be set if arg is bad
        return;
    }

    DPIF_LOG_DEBUG("RX Lcore %u: Periodic session update triggered.", ctx->lcore_id);

    const void *next_key_ptr;
    void *next_data_ptr;
    uint32_t iter = 0;
    uint32_t updated_count_this_iteration = 0;

    // Iterate over all sessions in this RX thread's hash table
    // This iteration is safe because it's performed by the owning RX thread.
    while (rte_hash_iterate(ctx->session_table, &next_key_ptr, &next_data_ptr, &iter) >= 0) {
        dpif_session_t *session = (dpif_session_t *) next_data_ptr;
        if (session) {  // Ensure session pointer is valid
            // Call the application's update callback for this session
            // The application can then check the session's state, apply policies, etc.
            int ret = ctx->registered_callbacks->dpi_session_update(session->libdpif_internal_sd);
            if (ret < 0) {
                DPIF_LOG_WARNING("RX Lcore %u: dpi_session_update for SD %d returned error %d.",
                                 ctx->lcore_id,
                                 session->libdpif_internal_sd,
                                 ret);
            }
            updated_count_this_iteration++;
        }
    }

    if (updated_count_this_iteration > 0) {
        rte_atomic64_add(&ctx->sessions_updated_by_timer, updated_count_this_iteration);
        DPIF_LOG_DEBUG(
            "RX Lcore %u: Called dpi_session_update for %u sessions.", ctx->lcore_id, updated_count_this_iteration);
    }
    // Timer is PERIODICAL, so it will automatically re-arm.
}

/**
 * @brief Callback function for session timeout.
 *
 * @param timer Pointer to the timer that expired (unused).
 * @param arg   Argument passed to rte_timer_reset, which is the dpif_session_t pointer.
 */
void dpif_session_timeout_callback(struct rte_timer *timer __rte_unused, void *arg) {
    dpif_session_t *session = (dpif_session_t *) arg;
    if (!session)
        return;
    uint32_t owner_lcore = session->owner_rx_lcore;
    if (owner_lcore >= RTE_MAX_LCORE || !g_dpif_ctx || !g_dpif_ctx->rx_contexts)
        return;
    dpif_rx_context_t *ctx = &g_dpif_ctx->rx_contexts[owner_lcore];
    if (!ctx->session_table)
        return;
    dpif_session_t *found_session = NULL;
    // Use the key stored within the session itself for lookup
    int ret = rte_hash_lookup_data(ctx->session_table, &session->key, (void **) &found_session);
    if (ret >= 0 && found_session == session) {
        DPIF_LOG_DEBUG("Session timeout for sd %d on core %u. Deleting.", session->libdpif_internal_sd, owner_lcore);
        rte_atomic64_inc(&ctx->session_timeouts);
        dpif_session_delete(ctx, session);
    } else {
        DPIF_LOG_DEBUG("Timeout callback for deleted/invalid session sd %d.", session->libdpif_internal_sd);
    }
}

/**
 * @brief Enqueues a packet to a session's internal queue.
 *
 * Tries the inline queue first, then the overflow mbuf chain if the inline queue is full.
 * @param session Pointer to the session.
 * @param mbuf Pointer to the mbuf to enqueue.
 * @param rx_ctx_for_drop_stats RX context for incrementing drop stats if session is NULL (not currently used for drops here).
 */
void dpif_session_q_enqueue_pkt(dpif_session_t *session,
                                struct rte_mbuf *mbuf,
                                dpif_rx_context_t *rx_ctx_for_drop_stats) {
    if (!session || !mbuf) {
        DPIF_LOG_WARNING("Enqueue: Invalid session (%p) or mbuf (%p)", (void *) session, (void *) mbuf);
        if (mbuf)
            rte_pktmbuf_free(mbuf);
        return;
    }

    // Try inline queue first
    if (session->inline_pkt_q_count < DPIF_SESSION_INLINE_PKT_QUEUE_SIZE) {
        session->inline_pkt_queue[session->inline_pkt_q_tail] = mbuf;
        session->inline_pkt_q_tail = (session->inline_pkt_q_tail + 1) % DPIF_SESSION_INLINE_PKT_QUEUE_SIZE;
        session->inline_pkt_q_count++;
        DPIF_LOG_DEBUG("SD %d: Enqueued packet to inline queue (count=%u)",
                       session->libdpif_internal_sd,
                       session->inline_pkt_q_count);
    } else {  // Inline queue is full, use overflow mbuf chain
        DPIF_LOG_DEBUG("SD %d: Inline queue full (%u), enqueueing to overflow chain.",
                       session->libdpif_internal_sd,
                       session->inline_pkt_q_count);
        mbuf->next = NULL;                 // Ensure it's the end of a new chain segment
        if (session->overflow_pkt_tail) {  // Chain already exists
            session->overflow_pkt_tail->next = mbuf;
            session->overflow_pkt_tail = mbuf;
        } else {  // Chain is empty, start it
            session->overflow_pkt_head = session->overflow_pkt_tail = mbuf;
        }
    }
}

/**
 * @brief Dequeues a packet from a session's internal queue.
 *
 * Tries the inline queue first, then the overflow mbuf chain.
 * @param session Pointer to the session.
 * @return struct rte_mbuf* Pointer to the dequeued mbuf, or NULL if the queue is empty.
 */
struct rte_mbuf *dpif_session_q_dequeue_pkt(dpif_session_t *session) {
    if (!session)
        return NULL;

    struct rte_mbuf *mbuf = NULL;

    // Try inline queue first
    if (session->inline_pkt_q_count > 0) {
        mbuf = session->inline_pkt_queue[session->inline_pkt_q_head];
        session->inline_pkt_queue[session->inline_pkt_q_head] = NULL;  // Help GC/debug
        session->inline_pkt_q_head = (session->inline_pkt_q_head + 1) % DPIF_SESSION_INLINE_PKT_QUEUE_SIZE;
        session->inline_pkt_q_count--;
        DPIF_LOG_DEBUG("SD %d: Dequeued packet from inline queue (count=%u)",
                       session->libdpif_internal_sd,
                       session->inline_pkt_q_count);
        return mbuf;
    }

    // Try overflow mbuf chain
    if (session->overflow_pkt_head) {
        mbuf = session->overflow_pkt_head;
        session->overflow_pkt_head = session->overflow_pkt_head->next;
        if (session->overflow_pkt_head == NULL) {  // Chain became empty
            session->overflow_pkt_tail = NULL;
            DPIF_LOG_DEBUG("SD %d: Dequeued last packet from overflow chain", session->libdpif_internal_sd);
        } else {
            DPIF_LOG_DEBUG("SD %d: Dequeued packet from overflow chain", session->libdpif_internal_sd);
        }
        mbuf->next = NULL;  // Detach from chain
        return mbuf;
    }

    // Queue is empty
    return NULL;
}

/**
 * @brief Cleans up all packets in a session's internal queues (inline and overflow).
 *
 * @param session Pointer to the session whose queues are to be cleaned.
 */
void dpif_session_q_cleanup(dpif_session_t *session) {
    if (!session)
        return;

    struct rte_mbuf *mbuf;
    uint32_t freed_count = 0;

    // Clean inline queue
    while (session->inline_pkt_q_count > 0) {
        mbuf = session->inline_pkt_queue[session->inline_pkt_q_head];
        if (mbuf) {
            rte_pktmbuf_free(mbuf);
            freed_count++;
        }
        session->inline_pkt_queue[session->inline_pkt_q_head] = NULL;
        session->inline_pkt_q_head = (session->inline_pkt_q_head + 1) % DPIF_SESSION_INLINE_PKT_QUEUE_SIZE;
        session->inline_pkt_q_count--;
    }
    session->inline_pkt_q_head = 0;  // Reset indices
    session->inline_pkt_q_tail = 0;
    // session->inline_pkt_q_count is already 0

    // Clean overflow chain
    mbuf = session->overflow_pkt_head;
    while (mbuf) {
        struct rte_mbuf *next_mbuf = mbuf->next;
        rte_pktmbuf_free(mbuf);
        freed_count++;
        mbuf = next_mbuf;
    }
    session->overflow_pkt_head = NULL;
    session->overflow_pkt_tail = NULL;

    if (freed_count > 0) {
        DPIF_LOG_DEBUG("SD %d: Cleaned up %u queued packets.", session->libdpif_internal_sd, freed_count);
    }
}

/**
 * @brief Looks up an existing session or creates a new one if not found.
 *
 * This function is central to session management. It uses a canonical flow key
 * for lookup in the hash table. If a session is found, its activity timer is reset.
 * If not found, a new session is allocated, initialized, and added to the hash table.
 * @param ctx Pointer to the RX thread context.
 * @param m Pointer to the mbuf.
 * @param flow_info Pointer to the parsed flow information for the current packet.
 * @return dpif_session_t* Pointer to the found or created session, or NULL on failure.
 */
dpif_session_t *
dpif_session_lookup_or_create(dpif_rx_context_t *ctx, const struct rte_mbuf *m, struct dpif_flow_info *flow_info) {
    int ret;
    uint64_t t0_create;
    dpif_session_t *s = NULL;
    dpi_flow_key_t flow_key_obj;

    if (!g_dpif_ctx || !ctx || !m || !flow_info || !ctx->session_table || !ctx->session_pool ||
        !ctx->registered_callbacks) {
        DPIF_LOG_ERROR("dpif_session_lookup_or_create: Invalid arguments or uninitialized context.");
        return NULL;
    }

    if (canonicalize_flow_key(flow_info, &flow_key_obj) < 0) {
        DPIF_LOG_ERROR("dpif_session_lookup_or_create: Failed to canonicalize flow key for AF %d.",
                       flow_info->address_family);
        return NULL;
    }

    ret = rte_hash_lookup_data(ctx->session_table, &flow_key_obj, (void **) &s);
    if (ret >= 0) {
        s->last_active_time = rte_rdtsc();
        if (g_dpif_ctx->session_timeout_ticks > 0) {
            rte_timer_reset(&s->timeout_timer,
                            g_dpif_ctx->session_timeout_ticks,
                            SINGLE,
                            ctx->lcore_id,
                            dpif_session_timeout_callback,
                            s);
        }
        DPIF_LOG_DEBUG("SD %d: Session found (AF: %d).", s->libdpif_internal_sd, flow_key_obj.address_family);
        return s;
    }

    if (ret == -ENOENT) {  // Session not found, need to create
        void *idx_ptr;
        // Get a free index from the ring
        if (rte_ring_sc_dequeue(ctx->free_session_indices_ring, &idx_ptr) != 0) {
            DPIF_LOG_WARNING("RX %u: No free session_idx available from ring (count %u).",
                             ctx->lcore_id,
                             rte_ring_count(ctx->free_session_indices_ring));
            return NULL;  // Session pool for this core is full (index-wise)
        }
        uint32_t new_session_idx = (uint32_t) (uintptr_t) idx_ptr;

        dpif_session_t *s;
        if (rte_mempool_get(ctx->session_pool, (void **) &s) < 0) {
            DPIF_LOG_ERROR("RX %u: Failed to get object from session_pool: %s", ctx->lcore_id, rte_strerror(rte_errno));
            // Rollback: return the index to the ring
            if (rte_ring_sp_enqueue(ctx->free_session_indices_ring, idx_ptr) != 0) {
                DPIF_LOG_ERROR("RX %u: Failed to rollback index %u to ring!", ctx->lcore_id, new_session_idx);
            }
            return NULL;
        }

        memset(s, 0, sizeof(dpif_session_t));
        s->key = flow_key_obj;  // flow_key_obj from canonicalize_flow_key
        s->owner_rx_lcore = ctx->lcore_id;
        s->session_idx_in_core = new_session_idx;  // Use macro or inline function
        s->libdpif_internal_sd = dpif_encode_sd(ctx->lcore_id, new_session_idx);

        const dp_metadata_t *mbuf_meta = DPIF_MBUF_METADATA(m);
        memcpy(&s->dp_metadata, mbuf_meta, sizeof(dp_metadata_t));

        rte_atomic32_init(&s->active_flag);
        rte_atomic64_init(&s->packets_fwd);
        rte_atomic64_init(&s->bytes_fwd);
        rte_atomic64_init(&s->packets_rev);
        rte_atomic64_init(&s->bytes_rev);
        s->inline_pkt_q_head = 0;
        s->inline_pkt_q_tail = 0;
        s->inline_pkt_q_count = 0;
        s->overflow_pkt_head = NULL;
        s->overflow_pkt_tail = NULL;
        s->is_task_running = 0;

        s->last_active_time = rte_rdtsc();
        rte_atomic32_set(&s->active_flag, 1);
        rte_timer_init(&s->timeout_timer);

        if (new_session_idx >= ctx->max_sessions_this_core) {
            DPIF_LOG_CRITICAL("RX %u: Obtained new_session_idx %u exceeds range %u!",
                              ctx->lcore_id,
                              new_session_idx,
                              ctx->max_sessions_this_core);
            // Critical error, clean up and return
            rte_mempool_put(ctx->session_pool, s);
            if (rte_ring_sp_enqueue(ctx->free_session_indices_ring, idx_ptr) != 0) { /* log error */
            }
            return NULL;
        }
        ctx->sessions_by_idx[new_session_idx] = s;

        // Call PML's dpi_session_create callback
        t0_create = rte_rdtsc();
        if (ctx->registered_callbacks->dpi_session_create(flow_info, s->libdpif_internal_sd) < 0) {
            DPIF_LOG_WARNING(
                "RX %u: PML's dpi_session_create callback failed (SD %d).", ctx->lcore_id, s->libdpif_internal_sd);
            ctx->sessions_by_idx[new_session_idx] = NULL;                            // Rollback
            if (rte_ring_sp_enqueue(ctx->free_session_indices_ring, idx_ptr) != 0) { /* log error */
            }  // Rollback
            rte_mempool_put(ctx->session_pool, s);  // Rollback
            return NULL;
        }
        rte_atomic64_add(&ctx->session_lookup_cycles, rte_rdtsc() - t0_create);

        // Add session to the 5-tuple hash table session_table
        ret = rte_hash_add_key_data(ctx->session_table, &s->key, s);
        if (ret < 0) {
            DPIF_LOG_ERROR("RX %u: Failed to add SD %d to session_table: %s",
                           ctx->lcore_id,
                           s->libdpif_internal_sd,
                           rte_strerror(-ret));
            if (ctx->registered_callbacks->dpi_session_destroy) {  // Attempt to rollback PML
                ctx->registered_callbacks->dpi_session_destroy(s->libdpif_internal_sd, s->app_data);
            }
            ctx->sessions_by_idx[new_session_idx] = NULL;
            if (rte_ring_sp_enqueue(ctx->free_session_indices_ring, idx_ptr) != 0) { /* log error */
            }
            rte_mempool_put(ctx->session_pool, s);
            return NULL;
        }

        DPIF_LOG_DEBUG("RX %u: Session SD %d (idx %u) created successfully.",
                       ctx->lcore_id,
                       s->libdpif_internal_sd,
                       new_session_idx);

        // Set timeout timer
        if (g_dpif_ctx && g_dpif_ctx->session_timeout_ticks > 0) {
            rte_timer_reset(&s->timeout_timer,
                            g_dpif_ctx->session_timeout_ticks,
                            SINGLE,
                            ctx->lcore_id,
                            dpif_session_timeout_callback,
                            s);
        }
        return s;
    } else if (ret < 0) {  // Other hash lookup errors
        DPIF_LOG_ERROR("RX %u: Session hash lookup error: %s (%d)", ctx->lcore_id, rte_strerror(-ret), ret);
        return NULL;
    }

    return NULL;
}

/**
 * @brief Deletes a session.
 *
 * Removes the session from the hash table, returns its index to the free pool,
 * cleans up its packet queue, calls the application's destroy callback, and returns the session object to its mempool.
 * @param ctx Pointer to the RX thread context that owns the session.
 * @param session Pointer to the session to be deleted.
 * @return 0 on success, negative errno on failure.
 */
int dpif_session_delete(dpif_rx_context_t *ctx, dpif_session_t *session) {
    if (!ctx || !session || !ctx->session_table || !ctx->session_pool || !ctx->registered_callbacks) {
        DPIF_LOG_ERROR("dpif_session_delete: Invalid arguments or uninitialized context.");
        return -EINVAL;
    }
    if (session->owner_rx_lcore != ctx->lcore_id) {
        DPIF_LOG_ERROR("dpif_session_delete: Session SD %d owner lcore %u does not match current context lcore %u.",
                       session->libdpif_internal_sd,
                       session->owner_rx_lcore,
                       ctx->lcore_id);
        return -EPERM;
    }

    uint32_t dpif_sd_to_delete = session->libdpif_internal_sd;
    uint32_t session_idx_to_free = session->session_idx_in_core;

    DPIF_LOG_DEBUG("RX %u: Deleting session SD %d (idx %u).", ctx->lcore_id, dpif_sd_to_delete, session_idx_to_free);

    rte_timer_stop_sync(&session->timeout_timer);

    // 1. Delete from the 5-tuple hash table session_table
    int ret_hash_del = rte_hash_del_key(ctx->session_table, &session->key);
    if (ret_hash_del < 0 && ret_hash_del != -ENOENT) {  // ENOENT can also be normal if already deleted concurrently
        DPIF_LOG_WARNING("RX %u: Failed to delete SD %d (key) from session_table: %s.",
                         ctx->lcore_id,
                         dpif_sd_to_delete,
                         rte_strerror(-ret_hash_del));
    }

    // 2. Remove from the sessions_by_idx array
    if (session_idx_to_free < ctx->max_sessions_this_core) {
        if (ctx->sessions_by_idx[session_idx_to_free] == session) {
            ctx->sessions_by_idx[session_idx_to_free] = NULL;
        } else {
            // If it doesn't match, it might have been modified by another operation, or the index calculation was wrong
            DPIF_LOG_ERROR("RX %u: When deleting SD %d, sessions_by_idx[idx %u] content mismatch (expected %p, got "
                           "%p).",
                           ctx->lcore_id,
                           dpif_sd_to_delete,
                           session_idx_to_free,
                           (void *) session,
                           (void *) ctx->sessions_by_idx[session_idx_to_free]);
        }
    } else {
        DPIF_LOG_ERROR("RX %u: When deleting SD %d, index %u is out of bounds (max %u).",
                       ctx->lcore_id,
                       dpif_sd_to_delete,
                       session_idx_to_free,
                       ctx->max_sessions_this_core);
    }

    // 3. Return the index to free_session_indices_ring
    if (session_idx_to_free < ctx->max_sessions_this_core) {  // Check again just in case
        if (rte_ring_sp_enqueue(ctx->free_session_indices_ring, (void *) (uintptr_t) session_idx_to_free) != 0) {
            DPIF_LOG_CRITICAL("RX %u: Failed to return index %u to free_session_indices_ring (SD %d)! Ring might be "
                              "full or "
                              "corrupted.",
                              ctx->lcore_id,
                              session_idx_to_free,
                              dpif_sd_to_delete);
            // This is a critical issue, may lead to index leak
        }
    }

    // 4. Clean up the session's internal queue
    dpif_session_q_cleanup(session);

    // 5. Call PML's dpi_session_destroy callback
    if (ctx->registered_callbacks && ctx->registered_callbacks->dpi_session_destroy) {
        ctx->registered_callbacks->dpi_session_destroy(dpif_sd_to_delete, session->app_data);
    }

    // 6. Return the session object to the mempool
    rte_mempool_put(ctx->session_pool, session);

    DPIF_LOG_DEBUG(
        "RX %u: Session SD %d (idx %u) deleted successfully.", ctx->lcore_id, dpif_sd_to_delete, session_idx_to_free);
    return 0;
}

/**
 * @brief Finds a session by its global session descriptor.
 *
 * Iterates through all RX cores and their session tables to find the session. This can be slow.
 * @param session_descriptor The global DPIF session descriptor.
 * @return dpif_session_t* Pointer to the found session, or NULL if not found.
 */
dpif_session_t *dpif_find_session_by_sd_global(int session_descriptor) {
    uint32_t owner_lcore_id = dpif_decode_sd_core_id(session_descriptor);
    uint32_t session_idx = dpif_decode_sd_index(session_descriptor);

    if (owner_lcore_id >= RTE_MAX_LCORE || !g_dpif_ctx->rx_contexts) {
        DPIF_LOG_ERROR("SD %d invalid owner_lcore_id %u", session_descriptor, owner_lcore_id);
        return NULL;
    }

    dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[owner_lcore_id];
    if (rx_ctx->lcore_id != owner_lcore_id) {
        return NULL;
    }

    if (session_idx >= rx_ctx->max_sessions_this_core || !rx_ctx->sessions_by_idx) {
        return NULL;
    }

    dpif_session_t *session = rx_ctx->sessions_by_idx[session_idx];

    return session;
}

/**
 * @brief Retrieves information for all active sessions across all RX cores.
 *
 * If `sessions_info_arr` is NULL, this function only fills `filled_count` with the total number of active sessions.
 * Otherwise, it fills `sessions_info_arr` up to `arr_max_size` entries.
 * @param sessions_info_arr Pointer to an array to store session information. Can be NULL.
 * @param arr_max_size Maximum number of entries `sessions_info_arr` can hold.
 * @param filled_count Output parameter, filled with the number of entries written to `sessions_info_arr` or total sessions if `sessions_info_arr` is NULL.
 * @return 0 on success, negative errno on failure.
 */
int dpif_get_all_session_info(dpif_session_info_t *sessions_info_arr, uint32_t arr_max_size, uint32_t *filled_count) {
    if (!filled_count)
        return -EINVAL;
    if (!g_dpif_ctx) {
        *filled_count = 0;
        DPIF_LOG_ERROR("dpif_get_all_session_info: DPIF context not initialized.");
        return -EPERM;
    }

    *filled_count = 0;
    uint32_t current_session_idx_in_arr = 0;
    uint32_t total_sessions_found_globally = 0;

    if (!g_dpif_ctx->rx_contexts) {
        DPIF_LOG_DEBUG("dpif_get_all_session_info: No RX contexts available.");
        return 0;
    }

    for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
        uint32_t lcore_id = g_dpif_ctx->rx_lcore_map[i];
        if (lcore_id >= RTE_MAX_LCORE || !rte_lcore_is_enabled(lcore_id)) {
            continue;
        }

        dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[lcore_id];
        if (rx_ctx->lcore_id != lcore_id || !rx_ctx->session_table) {
            continue;
        }

        const void *next_key_ptr_const;  // Iterate returns const void* for key
        void *next_data_ptr;
        uint32_t iter = 0;
        struct rte_hash *h = rx_ctx->session_table;

        while (rte_hash_iterate(h, &next_key_ptr_const, &next_data_ptr, &iter) >= 0) {
            total_sessions_found_globally++;
            if (sessions_info_arr != NULL && current_session_idx_in_arr < arr_max_size) {
                dpif_session_t *session = (dpif_session_t *) next_data_ptr;
                // The key stored in the session is dpi_flow_key_t.
                // It already contains IPs in NBO and ports in HBO.
                const dpi_flow_key_t *flow_key = &session->key;
                dpif_session_info_t *info = &sessions_info_arr[current_session_idx_in_arr];

                memset(info, 0, sizeof(dpif_session_info_t));
                info->address_family = flow_key->address_family;

                struct in_addr ipv4_addr_struct;
                struct in6_addr ipv6_addr_struct;

                if (info->address_family == AF_INET) {
                    // ip_lesser/greater in key are IPv4-mapped IPv6 addrs (NBO)
                    // Extract the last 4 bytes for IPv4 string representation
                    memcpy(&ipv4_addr_struct.s_addr, &flow_key->ip_lesser[12], sizeof(uint32_t));
                    inet_ntop(AF_INET, &ipv4_addr_struct, info->src_ip_str, INET6_ADDRSTRLEN);

                    memcpy(&ipv4_addr_struct.s_addr, &flow_key->ip_greater[12], sizeof(uint32_t));
                    inet_ntop(AF_INET, &ipv4_addr_struct, info->dst_ip_str, INET6_ADDRSTRLEN);
                } else {  // AF_INET6
                    memcpy(&ipv6_addr_struct, flow_key->ip_lesser, 16);
                    inet_ntop(AF_INET6, &ipv6_addr_struct, info->src_ip_str, INET6_ADDRSTRLEN);

                    memcpy(&ipv6_addr_struct, flow_key->ip_greater, 16);
                    inet_ntop(AF_INET6, &ipv6_addr_struct, info->dst_ip_str, INET6_ADDRSTRLEN);
                }
                // Ports in key are already host byte order
                info->src_port_real = flow_key->port_lesser;
                info->dst_port_real = flow_key->port_greater;

                info->proto = flow_key->proto;
                info->app_session_descriptor = session->libdpif_internal_sd;
                info->owner_rx_lcore = session->owner_rx_lcore;
                info->is_task_running = session->is_task_running;
                info->last_active_time_tsc = session->last_active_time;

                info->packets_fwd = rte_atomic64_read(&session->packets_fwd);
                info->bytes_fwd = rte_atomic64_read(&session->bytes_fwd);
                info->packets_rev = rte_atomic64_read(&session->packets_rev);
                info->bytes_rev = rte_atomic64_read(&session->bytes_rev);
                info->inline_pkt_q_count = session->inline_pkt_q_count;
                info->has_overflow_pkts = (session->overflow_pkt_head != NULL);

                current_session_idx_in_arr++;
            }
        }
    }

    if (sessions_info_arr == NULL) {
        *filled_count = total_sessions_found_globally;
    } else {
        *filled_count = current_session_idx_in_arr;
    }
    return 0;
}

int dpif_set_app_data(uint32_t dpif_sd, void *app_data) {
    dpif_session_t *session = dpif_find_session_by_sd_global(dpif_sd);
    if (!session) {
        DPIF_LOG_ERROR("Session not found for SD %d", dpif_sd);
        return -ENOENT;
    }

    session->app_data = app_data;
    DPIF_LOG_DEBUG("SD %d: App data set to %p", dpif_sd, app_data);
    return 0;
}

int dpif_get_app_data(uint32_t dpif_sd, void **app_data) {
    if (!app_data) {
        DPIF_LOG_ERROR("Invalid app_data pointer for SD %d", dpif_sd);
        return -EINVAL;
    }

    dpif_session_t *session = dpif_find_session_by_sd_global(dpif_sd);
    if (!session) {
        DPIF_LOG_ERROR("Session not found for SD %d", dpif_sd);
        return -ENOENT;
    }

    *app_data = session->app_data;
    DPIF_LOG_DEBUG("SD %d: App data retrieved: %p", dpif_sd, session->app_data);
    return 0;
}