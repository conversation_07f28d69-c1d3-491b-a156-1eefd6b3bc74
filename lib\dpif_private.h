#ifndef DPIF_PRIVATE_H
#define DPIF_PRIVATE_H

#include "dpif.h"
#include <rte_common.h>
#include <rte_hash.h>
#include <rte_log.h>

#include <rte_atomic.h>
#include <rte_cycles.h>
#include <rte_ethdev.h>
#include <rte_ether.h>
#include <rte_lcore.h>
#include <rte_mbuf.h>
#include <rte_mempool.h>
#include <rte_ring.h>
#include <rte_timer.h>
#include <rte_version.h>

#include "vpp_plugins/dpi_hook/vpp_dpi_msg.h"
#include <netinet/in.h>  // For AF_INET, AF_INET6, IPPROTO_TCP, IPPROTO_UDP
#include <string.h>

#include <arpa/inet.h>  // For inet_pton, inet_ntop, INET6_ADDRSTRLEN
#include <rte_jhash.h>  // For rte_jhash, often used with rte_hash

#define SIMULATE_PACKETS 0

// --- Constants ---
#define MAX_WORK_DATA_SIZE 10000              ///< Maximum size of data in a work item.
#define DPIF_SESSION_INLINE_PKT_QUEUE_SIZE 6  ///< Size of the inline packet queue per session.

#define MAX_SESSIONS_PER_RX_CORE_DEFAULT (1024 * 1024)  ///< Default maximum sessions per RX core.
#define DPIF_SD_CORE_ID_SHIFT 24                        ///< Bit shift for core ID in session descriptor.
#define DPIF_SD_CORE_ID_MASK (0xFFUL << DPIF_SD_CORE_ID_SHIFT)
#define DPIF_SD_INDEX_MASK (~DPIF_SD_CORE_ID_MASK)  // Or 0x00FFFFFFUL

#define DPIF_MBUF_METADATA(m) ((dp_metadata_t *) ((char *) (m) + sizeof(struct rte_mbuf)))

static inline uint32_t dpif_encode_sd(uint32_t core_id, uint32_t session_idx) {
    return ((core_id << DPIF_SD_CORE_ID_SHIFT) & DPIF_SD_CORE_ID_MASK) | (session_idx & DPIF_SD_INDEX_MASK);
}

static inline uint32_t dpif_decode_sd_core_id(int dpif_sd) {
    return (dpif_sd & DPIF_SD_CORE_ID_MASK) >> DPIF_SD_CORE_ID_SHIFT;
}

static inline uint32_t dpif_decode_sd_index(int dpif_sd) {
    return dpif_sd & DPIF_SD_INDEX_MASK;
}

#ifndef RTE_MAX_LCORE
#define RTE_MAX_LCORE 128
#endif

extern dpif_stats_snapshot_t *g_dpif_stats_snapshot;

/**
 * @brief Canonical flow key used for session lookup.
 * IPs are stored as IPv4-mapped IPv6 if originally IPv4. Ports are stored in host byte order.
 * IP_lesser/greater are in network byte order for consistent memcmp.
 */
typedef struct dpi_flow_key {
    uint8_t address_family;  ///< AF_INET or AF_INET6. Original address family of the flow.
    uint8_t proto;           ///< L4 protocol (e.g., IPPROTO_TCP, IPPROTO_UDP).
    uint16_t port_lesser;    ///< Numerically lesser of src/dst ports (host byte order).
    uint16_t port_greater;   ///< Numerically greater of src/dst ports (host byte order).
    uint8_t ip_lesser[16];   ///< Canonical lesser IP (IPv6 or IPv4-mapped IPv6, network byte order).
    uint8_t ip_greater[16];  ///< Canonical greater IP (IPv6 or IPv4-mapped IPv6, network byte order).
    uint8_t padding[2];      ///< Padding for alignment.
} dpi_flow_key_t;

/**
 * @brief Task distribution policies for RX threads.
 */
typedef enum {
    DPIF_TASK_DIST_RX_HASH_TO_WORKER,  ///< Distribute tasks to workers based on flow hash.
} dpif_task_distribution_t;

typedef struct dpif_session dpif_session_t;

/**
 * @brief Work item structure for offloading tasks to worker threads.
 */
struct dpi_work {
    uint16_t type, length;
    dpif_session_t *session_ptr;
    uint8_t data[];
};

/**
 * @brief Message structure for worker to RX completion notification.
 */
typedef struct dpif_completion_msg {
    dpif_session_t *session_ptr;
} dpif_completion_msg_t;

/**
 * @brief Represents a packet (potentially segmented) for DPI analysis.
 */
struct dpi_packet {
    struct rte_mbuf *current_mbuf, *first_mbuf;  ///< Current and first mbuf in the chain.
    int direction;                               ///< Packet direction (0 or 1).
    uint16_t current_offset;                     ///< Current offset within the current_mbuf.
    uint16_t total_payload_len;
    uint16_t bytes_yielded;
};

/**
 * @brief Metadata extracted from VPP's Netlink encapsulation or for sending verdicts.
 */
typedef struct {
    private_hdr_t priv_data;
    uint32_t in_if_index;   ///< Ingress interface index.
    uint32_t out_if_index;  ///< Egress interface index.

    uint16_t payload_offset;  ///< Offset to the inner packet payload within the mbuf.
    uint16_t payload_len;     ///< Length of the inner packet payload.

    struct rte_ether_addr smac;  ///< Outer source MAC address (for TX).
    struct rte_ether_addr dmac;  ///< Outer destination MAC address (for TX).
    uint32_t sip;                ///< Outer source IP address (NBO, for TX).
    uint32_t dip;                ///< Outer destination IP address (NBO, for TX).
    uint16_t sport;              ///< Outer source port (NBO, for TX).
    uint16_t dport;              ///< Outer destination port (NBO, for TX).
    uint32_t padding[4];
} dp_metadata_t;

/**
 * @brief Internal representation of a DPIF session.
 */
struct dpif_session {
    dpi_flow_key_t key;            ///< Canonical flow key for this session.
    void *app_data;                ///< Application-specific session context.
    uint32_t libdpif_internal_sd;  ///< DPIF internal session descriptor.
    uint32_t session_idx_in_core;  ///< Index of this session within its owner RX core's array.
    uint8_t is_task_running;       ///< Flag: 1 if a background task is running for this session.
    struct rte_mbuf *inline_pkt_queue[DPIF_SESSION_INLINE_PKT_QUEUE_SIZE];  ///< Inline queue for packets.
    uint8_t inline_pkt_q_head;                                              ///< Head index for the inline packet queue.
    uint8_t inline_pkt_q_tail;                                              ///< Tail index for the inline packet queue.
    uint8_t inline_pkt_q_count;          ///< Number of packets currently in the inline queue.
    struct rte_mbuf *overflow_pkt_head;  ///< Head of the mbuf chain for overflow packets.
    struct rte_mbuf *overflow_pkt_tail;  ///< Tail of the mbuf chain for overflow packets.
    uint32_t owner_rx_lcore;             ///< Lcore ID of the RX thread that owns this session.
    rte_atomic32_t active_flag;          ///< Atomic flag indicating if the session is active.
    uint64_t last_active_time;           ///< TSC timestamp of the last packet activity.
    rte_atomic64_t packets_fwd, bytes_fwd, packets_rev, bytes_rev;  ///< Per-session traffic counters.
    struct rte_timer timeout_timer;                                 ///< Timer for session timeout.
    void *__mempool_hdr;                                            ///< Mempool header, for internal mempool use.
    dp_metadata_t dp_metadata;  ///< Stored VPP metadata for this session (e.g., for sending verdicts).
    uint8_t inspected;
};

/**
 * @brief RX thread context.
 */
typedef struct {
    uint32_t lcore_id;        ///< Lcore ID this RX thread is running on.
    uint16_t memif_port_id;   ///< Memif port ID this RX thread polls.
    uint16_t memif_queue_id;  ///< Memif queue ID this RX thread polls.

    struct rte_hash *session_table;    ///< Hash table for session lookup on this core.
    struct rte_mempool *mbuf_pool;     ///< Mbuf pool.
    struct rte_mempool *session_pool;  ///< Session object pool.
    struct rte_mempool *work_pool;     ///< Work item pool.

    dpif_session_t **sessions_by_idx;            ///< Array of pointers to sessions, indexed by session_idx_in_core.
    uint32_t max_sessions_this_core;             ///< Actual max sessions configured for this core.
    struct rte_ring *free_session_indices_ring;  ///< Ring of free session indices for this core.

    struct rte_ring **worker_rings;    ///< Array of rings to send tasks to worker threads.
    struct rte_ring *completion_ring;  ///< Ring to receive completion messages from workers.

    uint32_t num_workers;                       ///< Number of worker threads this RX thread can dispatch to.
    dpif_task_distribution_t task_dist_policy;  ///< Policy for distributing tasks to workers.
    volatile int *quit_signal;                  ///< Pointer to the global quit signal.
    const dpi_device_t *registered_callbacks;   ///< Pointer to registered application callbacks.

    rte_atomic64_t rx_pkts;                    ///< Total packets received by this RX core.
    rte_atomic64_t dropped_pkts;               ///< Total packets dropped by this RX core.
    rte_atomic64_t tasks_offloaded;            ///< Total tasks offloaded to worker threads.
    rte_atomic64_t completion_msgs_processed;  ///< Total completion messages processed.
    rte_atomic64_t session_lookup_cycles;      ///< Cycles spent in session lookup.
    rte_atomic64_t analyze_cycles;             ///< Cycles spent in session analysis.
    rte_atomic64_t analyzed_pkts;
    rte_atomic64_t offload_cycles;            ///< Cycles spent in offloading tasks.
    rte_atomic64_t decap_cycles;              ///< Cycles spent in VPP header decapsulation.
    rte_atomic64_t benchmark_process_cycles;  ///< Cycles spent in benchmark processing.
    rte_atomic64_t session_timeouts;          ///< Number of sessions timed out on this core.

    struct rte_timer periodic_self_update_timer;  ///< Timer for periodic session updates by this RX core.
    rte_atomic64_t sessions_updated_by_timer;     ///< Counter for sessions updated by the periodic timer.

    rte_atomic32_t inject_packet_count;     ///< Number of packets to inject via CLI.
    volatile int pcap_replay_request;       // 1: A replay is requested
    char pcap_filepath[256];                // Path to the PCAP file
    rte_atomic32_t pcap_packets_to_inject;  // Number of packets to inject from PCAP
    uint8_t inject_address_family;          ///< AF_INET or AF_INET6 for injected packets.

    union {
        uint32_t ipv4_src_ip;     ///< Custom IPv4 source IP for injection (host byte order).
        uint8_t ipv6_src_ip[16];  ///< Custom IPv6 source IP for injection (network byte order).
    } inject_src_ip_u;

    uint16_t inject_src_port;  ///< Custom source port for injection.

    union {
        uint32_t ipv4_dst_ip;     ///< Custom IPv4 destination IP for injection (host byte order).
        uint8_t ipv6_dst_ip[16];  ///< Custom IPv6 destination IP for injection (network byte order).
    } inject_dst_ip_u;

    uint16_t inject_dst_port;        ///< Custom destination port for injection.
    uint8_t inject_proto;            ///< Custom protocol for injection.
    uint16_t inject_payload_len;     ///< Custom payload length for injection.
    volatile int inject_params_set;  ///< Flag: 1 if custom injection parameters are set by CLI.
    void *rx_thread_data;            ///< Thread-specific data for NSA
} dpif_rx_context_t;

/**
 * @brief Worker thread context.
 */
typedef struct {
    uint32_t lcore_id, worker_id, num_task_rings, num_rx_cores;
    struct rte_ring **task_rings, **rx_completion_rings;
    struct rte_mempool *work_pool;
    volatile int *quit_signal;
    const dpi_device_t *registered_callbacks;
    rte_atomic64_t processed_tasks, task_processing_cycles, notify_cycles;
} dpif_worker_context_t;

/**
 * @brief DPIF operating modes for benchmarking or special handling.
 */
typedef enum {
    DPIF_BENCHMARK_MODE_DEFAULT,  ///< Normal DPIF processing.
    DPIF_BENCHMARK_MODE_ECHO,     ///< Echo packets back to VPP with a default verdict.
    DPIF_BENCHMARK_MODE_DELAY,    ///< Simulate processing delay (currently drops packets).
    DPIF_BENCHMARK_MODE_RANDOM,   ///< Randomly drop or echo packets (not fully implemented).
    DPIF_BENCHMARK_MODE_MAX       ///< Maximum value for enum, for validation.
} dpif_benchmark_mode_t;

/**
 * @brief Global DPIF context structure.
 */
typedef struct {
    dpif_app_config_t app_cfg;                                 ///< Application configuration.
    dpi_device_t registered_callbacks;                         ///< Registered application callbacks.
    int callbacks_registered;                                  ///< Flag: 1 if callbacks are registered.
    struct rte_mempool *mbuf_pool, *session_pool, *work_pool;  ///< Global memory pools.
    struct rte_ring **all_task_rings, **all_completion_rings;  ///< Arrays of all task and completion rings.
    uint32_t num_total_task_rings, num_total_completion_rings, num_rx_cores, num_worker_cores;
    dpif_rx_context_t *rx_contexts;
    dpif_worker_context_t *worker_contexts;
    uint16_t memif_port_id;
    volatile int quit_signal;
    int eal_initialized_by_dpif;
    uint64_t session_timeout_ticks;
    uint64_t periodic_session_update_ticks;
    uint32_t rx_lcore_map[RTE_MAX_LCORE];             ///< Maps RX core index to lcore_id.
    uint32_t worker_lcore_map[RTE_MAX_LCORE];         ///< Maps Worker core index to lcore_id.
    uint32_t lcore_to_rx_idx_map[RTE_MAX_LCORE];      ///< Maps lcore_id to RX core index.
    uint32_t lcore_to_worker_idx_map[RTE_MAX_LCORE];  ///< Maps lcore_id to Worker core index.
    dpif_benchmark_mode_t benchmark_mode;             ///< Current benchmark/operating mode.
} dpif_global_context_t;

/**
 * @brief Structure to hold detailed information about an active session for display/query.
 */
typedef struct {
    uint8_t address_family;             ///< AF_INET or AF_INET6.
    char src_ip_str[INET6_ADDRSTRLEN];  ///< Source IP as string.
    char dst_ip_str[INET6_ADDRSTRLEN];  ///< Destination IP as string.
    uint16_t src_port_real;             ///< Source port (host byte order, from canonical key).
    uint16_t dst_port_real;             ///< Destination port (host byte order, from canonical key).
    uint8_t proto;                      ///< L4 protocol.

    int app_session_descriptor;     ///< Application-specific session descriptor (DPIF internal SD).
    uint32_t owner_rx_lcore;        ///< Lcore ID of the RX thread owning this session.
    uint8_t is_task_running;        ///< 1 if a background task is running for this session.
    uint64_t last_active_time_tsc;  ///< TSC timestamp of last activity.

    uint64_t packets_fwd;  ///< Packets in the forward direction (lesser to greater).
    uint64_t bytes_fwd;    ///< Bytes in the forward direction.
    uint64_t packets_rev;  ///< Packets in the reverse direction (greater to lesser).
    uint64_t bytes_rev;    ///< Bytes in the reverse direction.

    uint8_t inline_pkt_q_count;  ///< Number of packets in the inline queue.
    uint8_t has_overflow_pkts;   ///< 1 if there are packets in the mbuf overflow chain.
} dpif_session_info_t;

// --- Internal Function Prototypes ---
int dpif_rx_thread_main(void *arg);
void dpif_rx_process_completion_queue(dpif_rx_context_t *ctx);
int dpif_rx_enqueue_work(dpif_rx_context_t *ctx, dpif_session_t *session, struct dpi_work *work);
int dpif_worker_thread_main(void *arg);
void dpif_periodic_update_callback(struct rte_timer *timer, void *arg);
int dpif_worker_notify_completion(dpif_worker_context_t *ctx, dpif_session_t *session);
int canonicalize_flow_key(const struct dpif_flow_info *flow_info, dpi_flow_key_t *key);
dpif_session_t *
dpif_session_lookup_or_create(dpif_rx_context_t *ctx, const struct rte_mbuf *m, struct dpif_flow_info *flow_info);
int dpif_session_delete(dpif_rx_context_t *ctx, dpif_session_t *session);
void dpif_session_q_enqueue_pkt(dpif_session_t *session,
                                struct rte_mbuf *mbuf,
                                dpif_rx_context_t *rx_ctx_for_drop_stats);
struct rte_mbuf *dpif_session_q_dequeue_pkt(dpif_session_t *session);
void dpif_session_q_cleanup(dpif_session_t *session);
dpif_session_t *dpif_find_session_by_sd_global(int session_descriptor);
void dpif_session_timeout_callback(struct rte_timer *timer, void *arg);
void dpif_session_handle_periodic_updates(struct rte_timer *timer __rte_unused, void *arg);

void dpif_launch_threads(void);
void dpif_stop_threads(void);
void dpif_cleanup_resources(void);

struct rte_ring *dpif_get_worker_ring(uint32_t worker_id);
struct rte_ring *dpif_get_completion_ring(uint32_t rx_lcore_id);

int dpif_issue_verdict(dp_metadata_t *session, uint32_t verdict_decision, uint16_t tx_queue_id, dpif_msgtype_t type);
int dpif_benchmark_process_packet(dpif_rx_context_t *ctx, struct rte_mbuf *m);

void dpif_dump_dp_encapsulated_packet(struct rte_mbuf *m, const char *path);
void dpif_dump_verdict_message(struct rte_mbuf *m, const char *path_prefix);

#endif  // DPIF_PRIVATE_H
