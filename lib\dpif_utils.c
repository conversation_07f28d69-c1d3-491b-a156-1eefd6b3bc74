#include "dpif_private.h"
#include <rte_errno.h>

// Declare g_dpif_ctx as external
extern dpif_global_context_t *g_dpif_ctx;

/**
 * Get packet data from a DPI packet.
 * Supports chained mbufs.
 */
int dpi_packet_getdata(struct dpi_packet *packet, const uint8_t **data, int *dir) {
    if (!packet || !packet->current_mbuf || !data || !dir) {
        return 0;
    }

    if (packet->bytes_yielded >= packet->total_payload_len) {
        *data = NULL;
        return 0;
    }

    struct rte_mbuf *m = packet->current_mbuf;

    while (m) {
        uint16_t current_len_in_seg = rte_pktmbuf_data_len(m);

        if (packet->current_offset < current_len_in_seg) {
            uint16_t remaining_in_seg = current_len_in_seg - packet->current_offset;
            uint16_t remaining_logical_total = packet->total_payload_len - packet->bytes_yielded;
            uint16_t len_to_yield = (remaining_in_seg < remaining_logical_total) ? remaining_in_seg : remaining_logical_total;

            *data = rte_pktmbuf_mtod_offset(m, const uint8_t *, packet->current_offset);
            *dir = packet->direction;

            packet->current_offset += len_to_yield;
            packet->bytes_yielded += len_to_yield;
            return len_to_yield;
        }

        m = m->next;
        packet->current_mbuf = m;
        packet->current_offset = 0;
    }

    return 0;
}

/**
 * Allocate a dpi_work structure from the global work pool.
 */
struct dpi_work *dpi_allocate_work(size_t size) {
    if (!g_dpif_ctx || !g_dpif_ctx->work_pool) {
        DPIF_LOG_ERROR("Work pool not initialized for allocation");
        return NULL;
    }

    if (size > MAX_WORK_DATA_SIZE) {
        DPIF_LOG_ERROR("Work size %zu exceeds maximum allowed", size);
        return NULL;
    }

    struct dpi_work *work = NULL;
    if (rte_mempool_get(g_dpif_ctx->work_pool, (void **) &work) < 0) {
        DPIF_LOG_WARNING("Work allocation failed: %s", rte_strerror(rte_errno));
        return NULL;
    }

    return work;
}

/**
 * Set the type of a dpi_work item.
 */
void dpi_work_set_type(struct dpi_work *work, uint16_t type) {
    if (work) {
        work->type = type;
    }
}

/**
 * Set the length of the data carried by a dpi_work item.
 */
void dpi_work_set_length(struct dpi_work *work, uint16_t length) {
    if (work) {
        // Optional: validate against MAX_WORK_DATA_SIZE if needed
        work->length = length;
    }
}

/**
 * Get a pointer to the data buffer of a dpi_work item.
 */
uint8_t *dpi_work_get_data_buffer(struct dpi_work *work) {
    if (work) {
        return work->data;
    }
    return NULL;
}

/**
 * Schedule a dpi_work item to the appropriate session's worker.
 */
int dpif_schedule_work_for_session(uint32_t dpif_sd, struct dpi_work *work_item) {
    if (!g_dpif_ctx) {
        DPIF_LOG_ERROR("DPIF not initialized");
        return -EPERM;
    }

    if (!work_item) {
        DPIF_LOG_ERROR("work_item is NULL");
        return -EINVAL;
    }

    uint32_t owner_lcore_id = dpif_decode_sd_core_id(dpif_sd);
    uint32_t session_idx = dpif_decode_sd_index(dpif_sd);

    if (owner_lcore_id >= RTE_MAX_LCORE || !g_dpif_ctx->rx_contexts) {
        DPIF_LOG_ERROR("Invalid SD %d: owner_lcore_id %u", dpif_sd, owner_lcore_id);
        return -EINVAL;
    }

    dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[owner_lcore_id];

    // Strict check: verify that rx_ctx matches expected lcore
    if (rx_ctx->lcore_id != owner_lcore_id) {
        DPIF_LOG_ERROR(
            "Mismatch for SD %d: lcore %u vs rx_ctx->lcore_id %u", dpif_sd, owner_lcore_id, rx_ctx->lcore_id);
        return -EFAULT;  // SD may be stale or internal state is corrupted
    }

    if (session_idx >= rx_ctx->max_sessions_this_core || !rx_ctx->sessions_by_idx) {
        DPIF_LOG_ERROR(
            "Invalid session_idx %u (max %u) for SD %d", session_idx, rx_ctx->max_sessions_this_core, dpif_sd);
        return -EINVAL;
    }

    dpif_session_t *session = rx_ctx->sessions_by_idx[session_idx];

    if (!session) {
        DPIF_LOG_WARNING("Session not found for SD %d (idx %u on lcore %u)", dpif_sd, session_idx, owner_lcore_id);
        return -ENOENT;
    }

    // Verify session identity to avoid potential reuse/memory issues
    if (session->libdpif_internal_sd != dpif_sd || session->session_idx_in_core != session_idx) {
        DPIF_LOG_ERROR("Session ID mismatch for SD %d (idx %u). "
                       "Found: sd=%d, idx=%u. Possible reuse or corruption.",
                       dpif_sd,
                       session_idx,
                       session->libdpif_internal_sd,
                       session->session_idx_in_core);
        return -EBADF;
    }

    // Enqueue work to the session's RX context
    int ret_enqueue = dpif_rx_enqueue_work(rx_ctx, session, work_item);
    if (ret_enqueue != 0) {
        DPIF_LOG_ERROR("Failed to schedule work for SD %d. Error: %d", dpif_sd, ret_enqueue);
        return ret_enqueue;
    }

    return 0;
}

/**
 * @brief Dumps the content of a VPP verdict message for debugging.
 *
 * Parses and prints Ethernet, IP, UDP, VPP payload header, and Netlink message components.
 * @param m Pointer to the mbuf containing the verdict message.
 * @param path_prefix A string prefix for log messages (e.g., "TX Path").
 */
void dpif_dump_verdict_message(struct rte_mbuf *m, const char *path_prefix) {
    if (!m) {
        DPIF_LOG_INFO("%s: [mbuf NULL for verdict dump]", path_prefix);
        return;
    }

    DPIF_LOG_INFO("========== [VPP Verdict Message Dump] %s ==========", path_prefix);
    DPIF_LOG_INFO("Mbuf: pkt_len: %-5u  data_len: %-5u", m->pkt_len, m->data_len);

    uint8_t *data_ptr = rte_pktmbuf_mtod(m, uint8_t *);
    uint16_t current_len = rte_pktmbuf_data_len(m);
    uint16_t offset = 0;

    // 1. Ethernet Header
    if (current_len < offset + sizeof(eth_hdr_t)) {
        DPIF_LOG_ERROR("DumpVerdict: Too short for Eth header.");
        return;
    }
    eth_hdr_t *eth = (eth_hdr_t *) (data_ptr + offset);
    char smac_str[RTE_ETHER_ADDR_FMT_SIZE], dmac_str[RTE_ETHER_ADDR_FMT_SIZE];
    rte_ether_format_addr(smac_str, sizeof(smac_str), (struct rte_ether_addr *) eth->smac);
    rte_ether_format_addr(dmac_str, sizeof(dmac_str), (struct rte_ether_addr *) eth->dmac);
    DPIF_LOG_INFO(
        "Ethernet: SrcMAC: %s, DstMAC: %s, Proto: 0x%04x", smac_str, dmac_str, rte_be_to_cpu_16(eth->protocol));
    offset += sizeof(eth_hdr_t);

    // 2. IP Header
    if (current_len < offset + sizeof(ip4_hdr_t)) {
        DPIF_LOG_ERROR("DumpVerdict: Too short for IP header.");
        return;
    }
    ip4_hdr_t *ip = (ip4_hdr_t *) (data_ptr + offset);
    char sip_str[INET_ADDRSTRLEN], dip_str[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &ip->saddr, sip_str, sizeof(sip_str));  // saddr is NBO
    inet_ntop(AF_INET, &ip->daddr, dip_str, sizeof(dip_str));  // daddr is NBO
    DPIF_LOG_INFO("IP: SrcIP: %s(0x%x), DstIP: %s(0x%x), Proto: %u, TotLen: %u, ID: 0x%04x",
                  sip_str,
                  ip->saddr,
                  dip_str,
                  ip->daddr,
                  ip->protocol,
                  rte_be_to_cpu_16(ip->tot_len),
                  rte_be_to_cpu_16(ip->frag_id));
    offset += sizeof(ip4_hdr_t);  // Assume no IP options for simplicity

    // 3. UDP Header
    if (current_len < offset + sizeof(udp_hdr_t)) {
        DPIF_LOG_ERROR("DumpVerdict: Too short for UDP header.");
        return;
    }
    udp_hdr_t *udp = (udp_hdr_t *) (data_ptr + offset);
    DPIF_LOG_INFO("UDP: SrcPort: %u, DstPort: %u, Len: %u",
                  rte_be_to_cpu_16(udp->source),
                  rte_be_to_cpu_16(udp->dest),
                  rte_be_to_cpu_16(udp->len));
    offset += sizeof(udp_hdr_t);

    uint8_t msgtype = *(data_ptr + offset);
    offset += 1;

    // 4. private_hdr_t (VPP specific)
    if (current_len < offset + sizeof(private_hdr_t)) {
        DPIF_LOG_ERROR("DumpVerdict: Too short for VPP PayloadHdr.");
        return;
    }
    private_hdr_t *private_h = (private_hdr_t *) (data_ptr + offset), private;
    private_hdr_get(private_h, &private);
    DPIF_LOG_INFO("Private Header:");
    DPIF_LOG_INFO("  family      : %-5u  msgtype    : %-5u", private.family, msgtype);
    DPIF_LOG_INFO("  hook        : %-5u  dir        : %-5u", private.hook, private.dir);
    DPIF_LOG_INFO("  flow_thread : %-5u  sess_thread: %-5u", private.flow_thread, private.session_thread);
    DPIF_LOG_INFO("  session_id  : %-5u  seqno      : %-5u", private.session_id, private.seqno);
    offset += sizeof(private_hdr_t);

    // 5. Netlink Message (nlmsghdr, nfgenmsg, attributes)
    if (current_len < offset + sizeof(vpp_dpi_verdict_hdr_t)) {
        DPIF_LOG_ERROR("DumpVerdict: Too short for verdict hdr.");
        return;
    }
    DPIF_LOG_INFO("Verdict:");
    vpp_dpi_verdict_hdr_t *verdict_h = (vpp_dpi_verdict_hdr_t *) (data_ptr + offset);
    uint32_t verdict, mark;
    dpif_verdict_hdr_get((uint8_t *) verdict_h, &verdict, &mark);
    DPIF_LOG_INFO("  verdcit      : %-5u  mark    : %-5u", verdict, mark);

    DPIF_LOG_INFO("========== [End Verdict Message Dump] %s ==========", path_prefix);
}

// Suggested location: dpif_dump_utils.c (if created) or dpif_rx.c

/**
 * @brief Dumps the initial raw data of an mbuf in hexadecimal and ASCII format.
 *
 * Logs the first 'max_bytes_to_dump' bytes of the first segment of the mbuf.
 *
 * @param m The mbuf to dump.
 * @param log_prefix A prefix string for log messages (e.g., "RX RAW MBUF").
 * @param max_bytes_to_dump Maximum number of bytes to dump from the mbuf.
 */
static void dpif_dump_mbuf_raw_data(struct rte_mbuf *m, const char *log_prefix, int max_bytes_to_dump) {
    if (!m) {
        DPIF_LOG_INFO("%s: [mbuf is NULL]", log_prefix);
        return;
    }

    DPIF_LOG_INFO("========== [RAW MBUF DATA DUMP - %s] (First %d bytes) ==========", log_prefix, max_bytes_to_dump);
    DPIF_LOG_INFO("%s: Mbuf total pkt_len: %u, data_len (current segment): %u, nb_segs: %u, data_off: %u",
                  log_prefix,
                  m->pkt_len,
                  m->data_len,
                  m->nb_segs,
                  m->data_off);

    uint8_t *raw_data = rte_pktmbuf_mtod(m, uint8_t *);
    uint16_t current_segment_len = rte_pktmbuf_data_len(m);
    int bytes_to_actually_dump =
        (current_segment_len > (uint16_t) max_bytes_to_dump) ? max_bytes_to_dump : current_segment_len;

    if (bytes_to_actually_dump > 0) {
        char line_buf[256];  // Buffer for formatting each hex line
        int line_chars_written;
        const int bytes_per_line = 16;

        for (int i = 0; i < bytes_to_actually_dump; i += bytes_per_line) {
            line_chars_written = snprintf(line_buf, sizeof(line_buf), "    %s %04x: ", log_prefix, i);
            for (int j = 0; j < bytes_per_line; ++j) {
                if (i + j < bytes_to_actually_dump) {
                    line_chars_written += snprintf(
                        line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "%02x ", raw_data[i + j]);
                } else {
                    // Pad with spaces if the line is shorter than bytes_per_line
                    line_chars_written +=
                        snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "   ");
                }
            }
            // Add ASCII representation
            line_chars_written += snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, " | ");
            for (int j = 0; j < bytes_per_line; ++j) {
                if (i + j < bytes_to_actually_dump) {
                    char c = raw_data[i + j];
                    if (c >= 32 && c <= 126) {  // Printable ASCII
                        line_chars_written +=
                            snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "%c", c);
                    } else {
                        line_chars_written +=
                            snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, ".");
                    }
                } else {
                    break;  // Stop if we've run out of bytes to dump in this line
                }
            }
            DPIF_LOG_INFO("%s", line_buf);
        }
        if (bytes_to_actually_dump < current_segment_len) {
            DPIF_LOG_INFO("    %s ... (raw data in first segment truncated after %d bytes, total segment len: %u)",
                          log_prefix,
                          bytes_to_actually_dump,
                          current_segment_len);
        }
        if (m->nb_segs > 1) {
            DPIF_LOG_INFO(
                "    %s ... (mbuf has %u segments, only first segment's raw data shown above)", log_prefix, m->nb_segs);
        }
    } else {
        DPIF_LOG_INFO("%s: MBUF data is empty or not accessible in the first segment.", log_prefix);
    }
    DPIF_LOG_INFO("========== [End RAW MBUF DATA DUMP - %s] ==========", log_prefix);
}

/**
 * @brief Dumps the content of a VPP encapsulated packet for debugging.
 *
 * Parses and prints the outer VPP encapsulation headers and the inner packet details.
 * @param m Pointer to the mbuf containing the VPP encapsulated packet.
 * @param path A string identifier for log messages (e.g., "RX Path Input").
 */
void dpif_dump_dp_encapsulated_packet(struct rte_mbuf *m, const char *path) {
    if (!m) {
        DPIF_LOG_INFO("%s: [mbuf NULL]", path);
        return;
    }

    dpif_dump_mbuf_raw_data(m, path, 1024);

    dpif_msg_info_t msg_info;
    memset(&msg_info, 0, sizeof(msg_info));

    uint8_t *msg_hdr = rte_pktmbuf_mtod(m, uint8_t *);
    uint16_t msg_len = rte_pktmbuf_data_len(m);

    if (dpif_msg_parse(msg_hdr, msg_len, &msg_info) < 0) {
        DPIF_LOG_WARNING("%s: [Failed to parse VPP encapsulated packet]", path);
        return;
    }

    char smac_str[RTE_ETHER_ADDR_FMT_SIZE], dmac_str[RTE_ETHER_ADDR_FMT_SIZE];
    rte_ether_format_addr(smac_str, sizeof(smac_str), (struct rte_ether_addr *) msg_info.smac);
    rte_ether_format_addr(dmac_str, sizeof(dmac_str), (struct rte_ether_addr *) msg_info.dmac);

    char sip_str[INET_ADDRSTRLEN], dip_str[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &msg_info.sip, sip_str, sizeof(sip_str));
    inet_ntop(AF_INET, &msg_info.dip, dip_str, sizeof(dip_str));

    DPIF_LOG_INFO("========== [VPP Packet Dump] %s ==========", path);
    DPIF_LOG_INFO("Mbuf:");
    DPIF_LOG_INFO("  pkt_len     : %-5u  data_len   : %-5u", m->pkt_len, m->data_len);
    DPIF_LOG_INFO("Ethernet:");
    DPIF_LOG_INFO("  src_mac     : %-17s  dst_mac    : %-17s", smac_str, dmac_str);

    DPIF_LOG_INFO("IP/UDP (VPP Custom Semantics):");
    DPIF_LOG_INFO("  src_ip : 0x%08x dst_ip : 0x%08x", msg_info.sip, msg_info.dip);
    DPIF_LOG_INFO("  global session id   : 0x%016" PRIx64 "", GET_GLOBAL_SID(msg_info.sip, msg_info.dip));
    DPIF_LOG_INFO("  src_port    : %-5u           dst_port    : %-5u", msg_info.sport, msg_info.dport);  //

    if (msg_info.private_h) {
        private_hdr_t private;
        private_hdr_get(msg_info.private_h, &private);
        DPIF_LOG_INFO("Private Header:");
        DPIF_LOG_INFO("  family      : %-5u  msgtype    : %-5u", private.family, msg_info.msgtype);
        DPIF_LOG_INFO("  hook        : %-5u  dir        : %-5u", private.hook, private.dir);
        DPIF_LOG_INFO("  flow_thread : %-5u  sess_thread: %-5u", private.flow_thread, private.session_thread);
        DPIF_LOG_INFO("  session_id  : %-5u  seqno      : %-5u", private.session_id, private.seqno);
    }

    if (msg_info.msgtype == DPIF_MSG_PACKET_INSPECT) {
        uint32_t indev, outdev;
        uint16_t inner_len;
        uint8_t *inner;
        dpif_packet_hdr_get(msg_info.next_h, &indev, &outdev, &inner_len, &inner);
        DPIF_LOG_INFO("Meta data:");
        DPIF_LOG_INFO("In Interface Index: %u", indev);
        DPIF_LOG_INFO("Out Interface Index: %u", outdev);
        // Inner Packet Payload
        DPIF_LOG_INFO("----PAYLOAD (Inner Packet)----");
        if (inner_len > 0) {
            DPIF_LOG_INFO("  Inner Payload Raw Data (len: %d, displaying up to 128 bytes):", inner_len);
            const int max_dump_len = 128;
            int dump_len = (inner_len > max_dump_len) ? max_dump_len : inner_len;
            char line_buf[256];
            int line_chars_written;

            for (int i = 0; i < dump_len; i += 16) {
                line_chars_written = snprintf(line_buf, sizeof(line_buf), "    %04x: ", i);
                for (int j = 0; j < 16; ++j) {
                    if (i + j < dump_len) {  // int vs int
                        line_chars_written += snprintf(line_buf + line_chars_written,
                                                       sizeof(line_buf) - line_chars_written,
                                                       "%02x ",
                                                       inner[i + j]);
                    } else {
                        line_chars_written +=
                            snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "   ");
                    }
                }
                // ASCII
                line_chars_written +=
                    snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, " | ");
                for (int j = 0; j < 16; ++j) {  // int vs int
                    if (i + j < dump_len) {
                        char c = inner[i + j];
                        if (c >= 32 && c <= 126) {
                            line_chars_written +=
                                snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "%c", c);
                        } else {
                            line_chars_written +=
                                snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, ".");
                        }
                    } else {
                        break;
                    }
                }
                DPIF_LOG_INFO("%s", line_buf);
            }
            if (dump_len < inner_len) {
                DPIF_LOG_INFO("    ... (data truncated)");
            }
        } else {
            DPIF_LOG_INFO("  Inner Payload Raw Data: (empty)");
        }
        if ((size_t) inner_len >= sizeof(eth_hdr_t)) {
            eth_hdr_t *eth = (eth_hdr_t *) inner;
            char eth_smac[RTE_ETHER_ADDR_FMT_SIZE], eth_dmac[RTE_ETHER_ADDR_FMT_SIZE];
            rte_ether_format_addr(eth_smac, sizeof(eth_smac), (struct rte_ether_addr *) eth->smac);
            rte_ether_format_addr(eth_dmac, sizeof(eth_dmac), (struct rte_ether_addr *) eth->dmac);
            uint16_t eth_proto = bswap16(eth->protocol);

            DPIF_LOG_INFO("  Ether:");
            DPIF_LOG_INFO("    src_mac    : %-17s  dst_mac     : %-17s", eth_smac, eth_dmac);
            DPIF_LOG_INFO("    eth_proto  : 0x%04x", eth_proto);

            if (eth_proto == 0x0800 && (size_t) inner_len >= (sizeof(eth_hdr_t) + sizeof(ip4_hdr_t))) {
                ip4_hdr_t *ip = (ip4_hdr_t *) (inner + sizeof(eth_hdr_t));
                char ip_s[INET_ADDRSTRLEN], ip_d[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &ip->saddr, ip_s, sizeof(ip_s));
                inet_ntop(AF_INET, &ip->daddr, ip_d, sizeof(ip_d));
                DPIF_LOG_INFO("  IP:");
                DPIF_LOG_INFO("    src_ip     : %-15s  dst_ip      : %-15s", ip_s, ip_d);

                if (ip->protocol == 6 || ip->protocol == 17) {  // TCP or UDP
                    uint8_t *l4_ptr = inner + sizeof(eth_hdr_t) + sizeof(ip4_hdr_t);
                    if ((size_t) inner_len >= ((size_t) (l4_ptr - inner) + 4)) {
                        uint16_t sport = bswap16(*(uint16_t *) l4_ptr);
                        uint16_t dport = bswap16(*(uint16_t *) (l4_ptr + 2));
                        const char *proto_str = ip->protocol == 6 ? "TCP" : "UDP";
                        DPIF_LOG_INFO("  %s:", proto_str);
                        DPIF_LOG_INFO("    src_port   : %-5u  dst_port   : %-5u", sport, dport);
                    }
                }
            } else if (eth_proto == 0x86DD && (size_t) inner_len >= (sizeof(eth_hdr_t) + sizeof(struct ip6_hdr))) {
                struct ip6_hdr *ip6 = (struct ip6_hdr *) (inner + sizeof(eth_hdr_t));
                char ip_s[INET6_ADDRSTRLEN], ip_d[INET6_ADDRSTRLEN];
                inet_ntop(AF_INET6, &ip6->ip6_src, ip_s, sizeof(ip_s));
                inet_ntop(AF_INET6, &ip6->ip6_dst, ip_d, sizeof(ip_d));
                DPIF_LOG_INFO("  IPv6:");
                DPIF_LOG_INFO("    src_ip     : %-39s", ip_s);
                DPIF_LOG_INFO("    dst_ip     : %-39s", ip_d);

                if (ip6->ip6_nxt == 6 || ip6->ip6_nxt == 17) {
                    uint8_t *l4_ptr = inner + sizeof(eth_hdr_t) + sizeof(struct ip6_hdr);
                    if ((size_t) inner_len >= ((size_t) (l4_ptr - inner) + 4)) {
                        uint16_t sport = bswap16(*(uint16_t *) l4_ptr);
                        uint16_t dport = bswap16(*(uint16_t *) (l4_ptr + 2));
                        const char *proto_str = ip6->ip6_nxt == 6 ? "TCP" : "UDP";
                        DPIF_LOG_INFO("  %s:", proto_str);
                        DPIF_LOG_INFO("    src_port   : %-5u  dst_port   : %-5u", sport, dport);
                    }
                }
            }
        }
    } else if (msg_info.msgtype == DPIF_MSG_SESSION_DESTORY) {
        vpp_dpi_session_hdr_t *sess_data = (vpp_dpi_session_hdr_t *)(msg_info.next_h);
        DPIF_LOG_INFO("Session data:");
        DPIF_LOG_INFO("In Interface Index: %u", bswap32(sess_data->indev));
        DPIF_LOG_INFO("Out Interface Index: %u", bswap32(sess_data->outdev));
        if (sess_data->tuple.family == VPP_AF_IP4) {
            char ip_s[INET_ADDRSTRLEN], ip_d[INET_ADDRSTRLEN];
            uint32_t sip, dip;
            sip = bswap32(sess_data->tuple.ip4_addr[0]);
            dip = bswap32(sess_data->tuple.ip4_addr[1]);
            inet_ntop(AF_INET, &sip, ip_s, sizeof(ip_s));
            inet_ntop(AF_INET, &dip, ip_d, sizeof(ip_d));
            DPIF_LOG_INFO("  IP:");
            DPIF_LOG_INFO("    src_ip     : %-15s  dst_ip      : %-15s", ip_s, ip_d);
        } else {
            char ip_s[INET6_ADDRSTRLEN], ip_d[INET6_ADDRSTRLEN];
            inet_ntop(AF_INET6, sess_data->tuple.ip6_addr, ip_s, sizeof(ip_s));
            inet_ntop(AF_INET6, sess_data->tuple.ip6_addr + 16, ip_d, sizeof(ip_d));

            DPIF_LOG_INFO("  IPv6:");
            DPIF_LOG_INFO("    src_ip     : %-39s", ip_s);
            DPIF_LOG_INFO("    dst_ip     : %-39s", ip_d);
        }
        const char *proto_str = sess_data->tuple.proto == 6 ? "TCP" : "UDP";
        DPIF_LOG_INFO("  %s(%d):", proto_str, sess_data->tuple.proto);
        DPIF_LOG_INFO("    src_port   : %-5u  dst_port   : %-5u", 
                bswap16(sess_data->tuple.port[0]), bswap16(sess_data->tuple.port[1]));
        DPIF_LOG_INFO("  counters:");
        DPIF_LOG_INFO("    tx_pkts     : %" PRIu64 "\n", bswap64(sess_data->counter.tx_packets));
        DPIF_LOG_INFO("    tx_bytes    : %" PRIu64 "\n", bswap64(sess_data->counter.tx_bytes));
        DPIF_LOG_INFO("    rx_pkts     : %" PRIu64 "\n", bswap64(sess_data->counter.rx_packets));
        DPIF_LOG_INFO("    rx_bytes    : %" PRIu64 "\n", bswap64(sess_data->counter.rx_bytes));
    }
    DPIF_LOG_INFO("========== [End Dump] %s ==========", path);
}