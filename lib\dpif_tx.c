#include <netinet/in.h>  // For IPPROTO_UDP, AF_INET
#include <rte_ethdev.h>
#include <rte_mbuf.h>

#include "dpif_private.h"  // For dpif_session_t, dpif_global_context_t, DPIF_LOG

extern dpif_global_context_t *g_dpif_ctx;

/**
 * @brief Constructs and sends a verdict message to VPP.
 *
 * This function builds a Netlink-encapsulated packet containing the verdict
 * and other relevant metadata, then sends it via the configured memif port.
 * @param meta Pointer to the dp_metadata_t structure containing information from the original VPP packet and for the verdict.
 * @param verdict_decision The verdict to send (e.g., NF_ACCEPT, NF_DROP).
 * @return 0 on success, negative errno on failure.
 */
int dpif_issue_verdict(dp_metadata_t *meta, uint32_t verdict_decision, uint16_t tx_queue_id, dpif_msgtype_t type) {
    uint8_t *buf;
    if (!g_dpif_ctx || !g_dpif_ctx->mbuf_pool) {
        DPIF_LOG_ERROR("SendVerdict: Global context or mbuf_pool not initialized!");
        return -EFAULT;
    }
    struct rte_mbuf *m = rte_pktmbuf_alloc(g_dpif_ctx->mbuf_pool);
    if (!m) {
        DPIF_LOG_ERROR("SendVerdict: Failed to allocate mbuf for verdict.");
        return -ENOMEM;
    }

    buf = rte_pktmbuf_mtod(m, uint8_t *);
    uint8_t *ptr = buf;

    ptr += eth_hdr_put(ptr, (uint8_t *) &meta->smac, (uint8_t *) &meta->dmac);  // Dst: VPP MAC, Src: DPIF MAC

    ip4_hdr_t *ip_h = (ip4_hdr_t *) ptr;
    ptr += ip4_hdr_put(ptr, meta->sip, meta->dip, 0 /*payload_len*/, 0x0 /*frag_id*/, 0 /*tos*/, 0 /*csum*/);

    // 3. UDP Header
    udp_hdr_t *udp_h = (udp_hdr_t *) ptr;
    ptr += udp_hdr_put(ptr, meta->sport, meta->dport, 0 /*payload_len*/, 0 /*csum*/);

    ptr += dpif_msgtype_put(ptr, type);

    // 4. private_hdr_t (VPP specific)
    ptr += private_hdr_put(ptr, &meta->priv_data);

    // 5. Verdict
    ptr += dpif_verdict_hdr_put(ptr, verdict_decision, 0x0);

    // 6. Fix IP/UDP lengths and checksums
    uint16_t udp_total_len =
        sizeof(udp_hdr_t) + sizeof(private_hdr_t) + dpif_verdict_total_size() + DPIF_MSGTYPE_LEN;  // nl_payload_len = nfgenmsg + NLA
    uint16_t ip_total_len = sizeof(ip4_hdr_t) + udp_total_len;

    ip_h->tot_len = rte_cpu_to_be_16(ip_total_len);
    ip_h->check = 0;  // Clear for calculation
    ip_h->check = ip4_hdr_csum((uint8_t *) ip_h);

    udp_h->len = rte_cpu_to_be_16(udp_total_len);
    udp_h->check = 0;  // UDP checksum for verdict can be 0, or calculated:
    // udp_h->check = rte_ipv4_udptcp_cksum(ip_h, udp_h); (ensure ip_h and udp_h fields are correctly NBO filled)

    // 7. Set mbuf packet length
    uint16_t final_pkt_len = sizeof(eth_hdr_t) + ip_total_len;
    rte_pktmbuf_pkt_len(m) = final_pkt_len;
    rte_pktmbuf_data_len(m) = final_pkt_len;

    // Dump the constructed verdict packet before sending
    //dpif_dump_verdict_message(m, "TX Path Verdict Output");

    // 8. Send the verdict
    uint16_t nb_tx = rte_eth_tx_burst(g_dpif_ctx->memif_port_id, tx_queue_id, &m, 1);
    if (nb_tx < 1) {
        DPIF_LOG_DEBUG("SendVerdict: Failed to send VPP verdict packet via memif port %u queue %u for "
                       "original_packet_id %u.",
                       g_dpif_ctx->memif_port_id,
                       0,
                       meta->priv_data.seqno);
        rte_pktmbuf_free(m);
        return -EIO;  // rte_eth_tx_burst will free mbuf upon success (usually)
    }
    DPIF_LOG_DEBUG("SendVerdict: Successfully sent verdict for original_packet_id %u.", meta->priv_data.seqno);

    return 0;
}

/**
 * @brief Sets the verdict for a given session and sends it to VPP.
 *
 * @param libdpif_sd The DPIF internal session descriptor.
 * @param verdict The verdict to set (e.g., NF_ACCEPT, NF_DROP).
 * @return 0 on success, negative errno on failure.
 */
int dpif_set_session_verdict(uint32_t libdpif_sd, uint32_t verdict) {
    uint32_t owner_lcore_id = dpif_decode_sd_core_id(libdpif_sd);
    uint32_t session_idx = dpif_decode_sd_index(libdpif_sd);

    if (owner_lcore_id >= RTE_MAX_LCORE || !g_dpif_ctx->rx_contexts) {
        DPIF_LOG_ERROR("SD %d invalid owner_lcore_id %u", libdpif_sd, owner_lcore_id);
        return -EINVAL;
    }
    dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[owner_lcore_id];
    if (rx_ctx->lcore_id != owner_lcore_id) {
        return -EFAULT;
    }
    if (session_idx >= rx_ctx->max_sessions_this_core || !rx_ctx->sessions_by_idx) {
        return -EINVAL;
    }
    dpif_session_t *session = rx_ctx->sessions_by_idx[session_idx];

    if (!session || session->libdpif_internal_sd != libdpif_sd || session->session_idx_in_core != session_idx) {
        DPIF_LOG_WARNING("SD %d invalid.", libdpif_sd);
        return -ENOENT;
    }

    uint16_t tx_queue_id_for_verdict = rx_ctx->memif_queue_id;

    dpif_issue_verdict(&session->dp_metadata, verdict, tx_queue_id_for_verdict, 1);

    return 0;
}
