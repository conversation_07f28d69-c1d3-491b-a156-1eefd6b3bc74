#ifndef DPIF_INIT_H
#define DPIF_INIT_H

typedef struct {
    const char *dev_name;
    const char *socket_path;
    uint32_t role;
    uint32_t id;
    uint16_t num_queues;
    uint32_t buffer_size;
} dpif_memif_config_t;

typedef struct {
    uint32_t lcore_id;
    int is_rx_thread;
    uint16_t memif_queue_id;
    int is_worker_thread;
    uint32_t worker_group_id;
    void *user_context;
} dpif_core_config_t;

typedef struct {
    /* memif Configuration */
    dpif_memif_config_t memif_conf;

    /* Core/Thread Settings */
    dpif_core_config_t *core_configs;
    uint32_t num_core_configs;

    /* mbuf Pool Settings */
    const char *mbuf_pool_name;
    uint32_t mbuf_count;
    uint16_t mbuf_cache_size;

    /* Session Pool Settings */
    const char *session_pool_name;
    uint32_t session_count;
    uint16_t session_cache_size;
    uint32_t session_hash_entries;
    uint32_t session_timeout_seconds;

    /* Work Pool Settings */
    const char *work_pool_name;
    uint32_t work_count;
    uint16_t work_cache_size;

    /* Ring Settings */
    const char *task_ring_name_prefix;
    const char *completion_ring_name_prefix;
    uint32_t ring_size;
} dpif_internal_spec_t;

/* Global variable with default values */
extern dpif_internal_spec_t g_dpif_spec_settings;

#endif  // DPIF_PRIVATE_H
