// cli_client.c
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <errno.h> // For perror

#define CLI_SOCKET_PATH "/tmp/dpif_cli.sock" // Make sure this matches the server
#define CMD_BUFFER_SIZE 1024
#define RESPONSE_BUFFER_SIZE 4096 // For potentially long responses

int main() {
    int sock;
    struct sockaddr_un server_addr;
    char command[CMD_BUFFER_SIZE];
    char response[RESPONSE_BUFFER_SIZE];
    ssize_t n_read;

    sock = socket(AF_UNIX, SOCK_STREAM, 0);
    if (sock < 0) {
        perror("cli_client: socket");
        exit(EXIT_FAILURE);
    }

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sun_family = AF_UNIX;
    strncpy(server_addr.sun_path, CLI_SOCKET_PATH, sizeof(server_addr.sun_path) - 1);

    if (connect(sock, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("cli_client: connect to server failed. Is dpif_app running and CLI server started?");
        fprintf(stderr, "Attempted to connect to: %s\n", CLI_SOCKET_PATH);
        close(sock);
        exit(EXIT_FAILURE);
    }

    printf("Connected to DPIF CLI. Type 'help' for commands, 'quit_client' to exit this client, or 'quit' to ask server to close connection.\n");

    while (1) {
        printf("dpif-cli> "); // Or your preferred prompt
        fflush(stdout);

        if (fgets(command, sizeof(command), stdin) == NULL) {
            if (feof(stdin)) {
                printf("\nEOF received, exiting client.\n");
            } else {
                perror("cli_client: fgets");
            }
            break;
        }

        command[strcspn(command, "\r\n")] = 0; // Remove trailing newline

        if (strlen(command) == 0) { // Handle empty input from user
            continue;
        }

        if (strcmp(command, "quit_client") == 0) {
            printf("Exiting client as requested.\n");
            break;
        }

        if (write(sock, command, strlen(command)) < 0) {
            perror("cli_client: write to server");
            break;
        }

        if (strcmp(command, "quit") == 0) {
            // Server will close connection, read might return 0 or error.
            // We can try to read one last time for the server's "Closing connection" message.
            n_read = read(sock, response, sizeof(response) - 1);
            if (n_read > 0) {
                response[n_read] = '\0';
                printf("%s", response);
            } else if (n_read == 0) {
                printf("Server closed connection after 'quit' command.\n");
            } else {
                // perror("cli_client: read after sending 'quit'");
            }
            printf("Sent 'quit' to server. Exiting client.\n");
            break;
        }

        n_read = read(sock, response, sizeof(response) - 1);
        if (n_read < 0) {
            perror("cli_client: read from server");
            break;
        } else if (n_read == 0) {
            printf("Server closed connection unexpectedly.\n");
            break;
        }

        response[n_read] = '\0';
        printf("%s", response); // Server response should include newlines as needed
    }

    close(sock);
    return 0;
}