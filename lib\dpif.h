#ifndef DPIF_H
#define DPIF_H

#include <daemonlib.h> /* Daemon framework */
#include <stddef.h>    // For size_t, used in dpi_allocate_work
#include <stdint.h>    // For uintN_t types like uint8_t, uint16_t, uint32_t, uint64_t

/* ========================================================================
 * DPIF Logging System (using dl_log_print)
 * ======================================================================== */

/**
 * @brief DPIF log levels (aligned with NSA log levels)
 */
typedef enum {
    DPIF_LOG_EMERGENCY = 0, /**< System unusable */
    DPIF_LOG_ALERT = 1,     /**< Alert conditions (immediate action required) */
    DPIF_LOG_CRITICAL = 2,  /**< Critical conditions */
    DPIF_LOG_ERROR = 3,     /**< Error conditions */
    DPIF_LOG_WARNING = 4,   /**< Warning conditions */
    DPIF_LOG_NOTICE = 5,    /**< Notable conditions */
    DPIF_LOG_INFO = 6,      /**< General information */
    DPIF_LOG_DEBUG = 7      /**< Debug information */
} dpif_log_level_t;

/* DPIF Logging macros */
#define DPIF_LOG_EMERGENCY(fmt, ...) \
    dl_log_print(DPIF_LOG_EMERGENCY, "[DPIF_EMERG] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define DPIF_LOG_ALERT(fmt, ...) \
    dl_log_print(DPIF_LOG_ALERT, "[DPIF_ALERT] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define DPIF_LOG_CRITICAL(fmt, ...) \
    dl_log_print(DPIF_LOG_CRITICAL, "[DPIF_CRIT]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define DPIF_LOG_ERROR(fmt, ...) \
    dl_log_print(DPIF_LOG_ERROR, "[DPIF_ERROR] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define DPIF_LOG_WARNING(fmt, ...) \
    dl_log_print(DPIF_LOG_WARNING, "[DPIF_WARN]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define DPIF_LOG_NOTICE(fmt, ...) \
    dl_log_print(DPIF_LOG_NOTICE, "[DPIF_NOTE]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define DPIF_LOG_INFO(fmt, ...) \
    dl_log_print(DPIF_LOG_INFO, "[DPIF_INFO]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define DPIF_LOG_DEBUG(fmt, ...) \
    dl_log_print(DPIF_LOG_DEBUG, "[DPIF_DEBUG] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

/* ========================================================================
 * DPIF Core API
 * ======================================================================== */

///< Opaque structure representing parsed flow information.
struct dpi_packet;
///< Opaque structure representing a packet for analysis.
struct dpi_work;

///< Opaque structure representing a work item for background processing.

/**
 * @brief Verdict codes to be returned by the dpi_session_analyze_t callback.
 * * These codes inform libdpif how to handle the packet and the session.
 */
typedef enum {
    /**
     * @brief The connection is definitively allowed.
     * * libdpif will issue an ACCEPT verdict for the session. Future packets may
     * still be analyzed unless a session bypass mechanism is implemented.
     */
    DPI_VERDICT_PERMITTED = 0,

    /**
     * @brief The connection is malicious or should be blocked.
     * * libdpif will issue a DROP verdict and immediately delete the session.
     */
    DPI_VERDICT_DROP = 1,

    /**
     * @brief Further analysis is required for this session.
     * * libdpif will issue an ACCEPT verdict for the current packet to allow it to pass,
     * but the session remains under active inspection for subsequent packets.
     */
    DPI_VERDICT_PENDING = 2,

    /**
     * @brief Further analysis is required for this session.
     * * libdpif will issue an ACCEPT verdict for the current packet to allow it to pass,
     * but the session remains under active inspection for subsequent packets.
     */
    DPI_VERDICT_UNKNOWN = 3,

    /**
     * @brief An error occurred during analysis.
     * * The packet will be dropped by libdpif, but the session will be kept alive
     * for potential future analysis. No verdict is sent to the dataplane.
     */
    DPI_VERDICT_ERROR = -1,
} dpi_verdict_t;

/**
 * @brief Represents parsed flow information from a packet.
 * IP addresses and ports are stored in network byte order as extracted from the packet.
 */
struct dpif_flow_info {
    uint8_t address_family;  ///< Address family: AF_INET or AF_INET6.

    union {
        uint32_t ipv4_src_ip;     ///< Source IPv4 address (network byte order).
        uint8_t ipv6_src_ip[16];  ///< Source IPv6 address (network byte order).
    } src_ip_u;                   ///< Union for source IP address.

    union {
        uint32_t ipv4_dst_ip;     ///< Destination IPv4 address (network byte order).
        uint8_t ipv6_dst_ip[16];  ///< Destination IPv6 address (network byte order).
    } dst_ip_u;                   ///< Union for destination IP address.

    uint16_t src_port;           ///< Source L4 port (network byte order).
    uint16_t dst_port;           ///< Destination L4 port (network byte order).
    uint8_t proto;               ///< L4 protocol (e.g., IPPROTO_TCP, IPPROTO_UDP).
    uint16_t l4_payload_offset;  ///< Offset from mbuf data start to L4 payload.
    uint16_t l4_payload_len;     ///< Length of the L4 payload.
    int direction;               ///< Flow direction (0 or 1) relative to canonical key.
    uint8_t src_mac[6];          ///< Source Device MAC address.
    uint8_t src_zone;            ///< Source Zone.
    uint8_t dst_zone;            ///< Destination Zone.
};

#define DPIF_MAX_MONITORED_CORES 32  // A reasonable upper limit for core stats

/**
 * @brief A snapshot of statistics for a single core (RX or Worker).
 * This structure contains cumulative totals since DPIF started.
 */
typedef struct {
    uint32_t lcore_id;
    char type[8];  // "RX", "Worker", or empty if inactive

    // Cumulative counters
    uint64_t rx_pkts;
    uint64_t dropped_pkts;
    uint64_t tasks_offloaded;
    uint64_t completion_msgs_processed;
    uint64_t sessions;
    uint64_t session_timeouts;
    uint64_t processed_tasks;
    uint64_t updated_sessions_by_timer;

    // Ring buffer stats (instantaneous values)
    uint32_t ring_count;
    uint32_t ring_capacity;

} dpif_core_stats_snapshot_t;

/**
 * @brief A snapshot of the entire DPIF system's statistics.
 * This is the main structure shared between DPIF and external modules.
 */
typedef struct {
    uint64_t tsc;
    dpif_core_stats_snapshot_t core_stats[DPIF_MAX_MONITORED_CORES];
} dpif_stats_snapshot_t;

// --- DPI Application Callbacks ---

/**
 * @brief Callback invoked when a new session is created.
 * @param flow_info Information about the flow that triggered session creation.
 * @param dpif_sd The DPIF-assigned session descriptor for this new session.
 * @return 0 on success, negative error code on failure (session creation will be aborted by DPIF).
 */
typedef int (*dpi_session_create_t)(const struct dpif_flow_info *flow_info, uint32_t dpif_sd);
/**
 * @brief Callback invoked when a session is being destroyed.
 * @param dpif_sd The DPIF session descriptor of the session being destroyed.
 * @param app_data NSA's private data.
 * @return 0 on success, negative error code on failure (DPIF will still proceed with its internal cleanup).
 */
typedef int (*dpi_session_destroy_t)(uint32_t dpif_sd, void *app_data);

/**
 * @brief Callback invoked to analyze a packet belonging to an existing session.
 * @param dpif_sd The DPIF session descriptor.
 * @param packet Pointer to the packet data structure. Use dpi_packet_getdata() to access packet content.
 * @return Application-defined integer. Typically, >= 0 might indicate success/verdict, and < 0 an error.
 *         The interpretation of this return value is up to the application and how it interacts with dpif_set_session_verdict.
 */
typedef int (*dpi_session_analyze_t)(uint32_t dpif_sd, struct dpi_packet *packet);

/**
 * @brief Callback invoked by a worker thread to process an offloaded work item.
 * @param dpif_sd The DPIF session descriptor associated with this work.
 * @param work Pointer to the work item. Use dpi_work_get_data_buffer() etc. to access work content.
 * @return 0 on success, negative error code on failure.
 */
typedef int (*dpi_session_work_t)(uint32_t dpif_sd, struct dpi_work *work);

/**
 * @brief Callback invoked periodically for each active session on its owning RX core.
 * Allows the application to perform periodic checks or updates for the session.
 * @param dpif_sd The DPIF session descriptor.
 * @return 0 on success, negative error code on failure.
 */
typedef int (*dpi_session_update_t)(uint32_t dpif_sd);

/**
 * @brief Callback invoked when an RX thread is initialized.
 * Allows the application to set up thread-specific data for each RX thread.
 * @param lcore_id The lcore ID of the RX thread being initialized.
 * @param thread_data Pointer to store thread-specific data (allocated by application).
 * @return 0 on success, negative error code on failure.
 */
typedef int (*dpi_rx_thread_init_t)(uint32_t lcore_id, void **thread_data);

/**
 * @brief Callback invoked when an RX thread is being cleaned up.
 * Allows the application to clean up thread-specific data.
 * @param lcore_id The lcore ID of the RX thread being cleaned up.
 * @param thread_data Thread-specific data to be cleaned up.
 * @return 0 on success, negative error code on failure.
 */
typedef int (*dpi_rx_thread_cleanup_t)(uint32_t lcore_id, void *thread_data);

/**
* @brief Callback invoked periodically on each RX thread's slow path.
* 
* This allows the application (NSA) to perform periodic maintenance tasks
* like checking for configuration updates, cleaning up resources, or, in this
* case, checking for PML hot-reload requests.
* This callback is passed the application's thread-specific data.
* 
* @param rx_thread_data The thread-specific data pointer provided by the
*                       application during dpi_rx_thread_init.
*/
typedef void (*dpi_periodic_maintenance_t)(void *rx_thread_data);

typedef struct dpi_device {
    dpi_session_create_t dpi_session_create;
    dpi_session_destroy_t dpi_session_destroy;
    dpi_session_analyze_t dpi_session_analyze;
    dpi_session_work_t dpi_session_work;
    dpi_session_update_t dpi_session_update;
    dpi_rx_thread_init_t dpi_rx_thread_init;
    dpi_rx_thread_cleanup_t dpi_rx_thread_cleanup;
    dpi_periodic_maintenance_t dpi_periodic_maintenance;
} dpi_device_t;

// --- DPIF Configuration ---
/**
 * @brief Application configuration for DPIF initialization.
 */
typedef struct {
    const char *app_name_prefix;       ///< Prefix for DPDK objects (pools, rings).
    uint32_t num_rx_threads;           ///< Number of RX threads to create.
    uint32_t num_worker_threads;       ///< Number of worker threads to create.
    uint32_t max_sessions;             ///< Maximum total sessions across all cores.
    uint32_t session_timeout_seconds;  ///< Session inactivity timeout in seconds. 0 to disable.
    uint64_t
        session_update_interval_seconds;  ///< Interval for periodic session_update callback in seconds. 0 to disable.
} dpif_app_config_t;

// --- DPIF API Functions ---

/**
 * @brief Registers the DPI application's callback functions with the DPIF library.
 * Must be called before dpif_init().
 * @param dev Pointer to a dpi_device_t structure containing the callback function pointers.
 * @return 0 on success, negative error code on failure.
 */
int dpi_register_device(const dpi_device_t *dev);

/**
 * @brief Unregisters the DPI application's callback functions.
 */
void dpi_unregister_device(void);

/**
 * @brief Phase 1: Initializes DPIF resources but does not start threads or devices.
 * After this function successfully returns, the application (e.g., NSA) can safely
 * initialize its own resources that depend on DPDK being active.
 * @param config Pointer to the application configuration structure.
 * @return 0 on success, negative error code on failure.
 */
int dpif_prepare(const dpif_app_config_t *config);

/**
 * @brief Phase 2: Starts the DPIF data plane.
 * This function must be called after dpif_prepare() and after the application
 * has finished its own resource initialization. It performs the following:
 * 1. Starts the configured memif device.
 * 2. Launches all RX and Worker threads.
 * @return 0 on success, negative error code on failure.
 */
int dpif_start(void);

/**
 * @brief Signals all DPIF threads to stop processing and exit.
 * This function is non-blocking. Call dpif_cleanup() to wait for threads and clean up resources.
 */
void dpif_stop(void);

/**
 * @brief Cleans up all DPIF resources and the DPDK EAL.
 * Waits for all DPIF threads to exit before cleaning up.
 */
void dpif_cleanup(void);

/**
 * @brief Get thread-specific data for the current RX thread
 * 
 * Retrieves the thread-specific data that was set during RX thread initialization.
 * This function can only be called from within an RX thread context.
 * 
 * @return Pointer to thread-specific data, or NULL if not available or not in RX thread context
 */
void *dpif_get_current_rx_thread_data(void);

// --- Helper API ---

/**
 * @brief Retrieves a chunk of packet data from a dpi_packet structure.
 * Handles segmented mbufs automatically.
 * @param packet Pointer to the dpi_packet structure.
 * @param data Output pointer to the data chunk.
 * @param dir Output pointer to the direction of the packet segment.
 * @return Length of the data chunk in bytes, or 0 if no more data.
 */
int dpi_packet_getdata(struct dpi_packet *packet, const uint8_t **data, int *dir);

/**
 * @brief Allocates a dpi_work structure from the global work pool.
 * @param size The size of the data payload required for this work item (max MAX_WORK_DATA_SIZE).
 * @return Pointer to the allocated dpi_work structure, or NULL on failure.
 */
struct dpi_work *dpi_allocate_work(size_t size);

/** @brief Sets the type field of a dpi_work item. */
void dpi_work_set_type(struct dpi_work *work, uint16_t type);

/** @brief Sets the length field of a dpi_work item (length of actual data in the buffer). */
void dpi_work_set_length(struct dpi_work *work, uint16_t length);

/** @brief Gets a pointer to the data buffer within a dpi_work item. */
uint8_t *dpi_work_get_data_buffer(struct dpi_work *work);

/**
 * @brief Schedules a dpi_work item for processing by a worker thread associated with the given session.
 * The DPIF library takes ownership of the work_item if successfully scheduled.
 * @param dpif_sd The DPIF session descriptor.
 * @param work_item Pointer to the dpi_work structure to be scheduled.
 * @return 0 on success, negative error code on failure (e.g., queue full, invalid session).
 */
int dpif_schedule_work_for_session(uint32_t dpif_sd, struct dpi_work *work_item);

/**
 * @brief Sets session verdict that will be sent to the dataplane.
 * @param libdpif_sd The DPIF session descriptor.
 * @param verdict The verdict to set for this session.
 * @return 0 on success, negative error code on failure.
 */
int dpif_set_session_verdict(uint32_t libdpif_sd, uint32_t verdict);

// --- Session User Data API ---

/**
 * @brief Associate user data with a session
 * @param dpif_sd The DPIF session descriptor
 * @param user_data Pointer to user data to associate with the session
 * @return 0 on success, negative error code on failure
 */
int dpif_set_app_data(uint32_t dpif_sd, void *user_data);

/**
 * @brief Retrieve user data associated with a session
 * @param dpif_sd The DPIF session descriptor
 * @param user_data Output pointer to receive the user data pointer
 * @return 0 on success, negative error code on failure
 */
int dpif_get_app_data(uint32_t dpif_sd, void **user_data);

int dpif_execute_cli_command(const char *command, char *response_buf, size_t buf_size);

int dpif_get_stats_snapshot(dpif_stats_snapshot_t *snapshot);

void dpif_config_init(int dpu_id, int sim);

#endif  // DPIF_H
