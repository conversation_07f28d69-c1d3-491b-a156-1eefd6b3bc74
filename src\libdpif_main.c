/*
 * *************************************************************************************
 *
 *       Filename:  libdpif_test.c
 *
 *    Description:  Template
 *
 *        Version:  1.0
 *        Created:  04/09/2025 02:08:37 AM
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  <PERSON>, pier<PERSON>.<EMAIL>
 *   Organization:  Calix
 *
 * *************************************************************************************
 */

#include <../lib/dpif.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>  // For rand()
#include <string.h>
#include <unistd.h>  // For sleep

int monitor_session_create(const struct dpif_flow_info *flow_info, uint32_t dpif_sd) {
    return 0;
}

int monitor_session_destroy(uint32_t dpif_sd, void *app_data) {
    return 0;
}

int monitor_session_analyze(uint32_t dpif_sd, struct dpi_packet *packet) {
    const uint8_t *data;
    int dir;
    int len = dpi_packet_getdata(packet, &data, &dir);  // Consume some data

    //if ((rand() % 100) < 5) { // Simulate an error
    //    return -EAGAIN; // Example error
    //}

    // Simulate PML deciding to offload work
    if (len > 0 && (rand() % 100) < 10) {  // For 10% of packets with data

        //printf("Monitor App: SD %d, analyze wants to schedule background work.", dpif_sd);
        struct dpi_work *work = dpi_allocate_work(64);  // Allocate work item

        if (work) {
            dpi_work_set_type(work, 1);  // Example work type
            dpi_work_set_type(work, 1);  // Example data length
            uint8_t *data_buf = dpi_work_get_data_buffer(work);
            if (data_buf) {
                snprintf((char *) data_buf, 10, "BG Data");  // Populate data
            } else {
                fprintf(stderr, "Monitor App: Failed to get data buffer for work SD %d.\n", dpif_sd);
            }

            // PML calls the new API to schedule work
            int schedule_ret = dpif_schedule_work_for_session(dpif_sd, work);
            if (schedule_ret != 0) {
                fprintf(stderr, "Monitor App: Failed to schedule work for SD %d. Freeing work item.\n", dpif_sd);
            } else {
                //printf("Monitor App: Successfully scheduled work for SD %d.", dpif_sd);
            }
        } else {
            fprintf(stderr, "Monitor App: Failed to allocate work for SD %d.\n", dpif_sd);
        }
        //dpif_set_session_verdict(dpif_sd, 1);
        return 0;
    }

    // Simulate "continue" vs "done"
    if ((rand() % 100) < 80) {
        return 0;  // Continue
    } else {
        return 0;  // Done
    }
}

int monitor_session_work(uint32_t dpif_sd, struct dpi_work *work) {
    return 0;
}

int monitor_session_update(uint32_t dpif_id) {
    return 0;
}

static volatile int force_quit = 0;

static void signal_handler(int signum) {
    if (signum == SIGINT || signum == SIGTERM) {
        printf("\nSignal %d received, preparing to exit...\n", signum);
        force_quit = 1;
    }
}

static dpif_app_config_t dpu_main_app_config = {.app_name_prefix = "dpif_test",
                                                .num_rx_threads = 8,
                                                .num_worker_threads = 2,
                                                .max_sessions = 1000000,
                                                .session_timeout_seconds = 300,
                                                .session_update_interval_seconds = 1800};

static dpif_app_config_t dpu_n_app_config = {.app_name_prefix = "dpif_test",
                                             .num_rx_threads = 16,
                                             .num_worker_threads = 2,
                                             .max_sessions = 1000000,
                                             .session_timeout_seconds = 300,
                                             .session_update_interval_seconds = 1800};

static dpif_app_config_t sim_main_app_config = {.app_name_prefix = "dpif_test",
                                                .num_rx_threads = 2,
                                                .num_worker_threads = 1,
                                                .max_sessions = 1000000,
                                                .session_timeout_seconds = 300,
                                                .session_update_interval_seconds = 1800};

static dpif_app_config_t sim_n_app_config = {.app_name_prefix = "dpif_test",
                                             .num_rx_threads = 2,
                                             .num_worker_threads = 1,
                                             .max_sessions = 1000000,
                                             .session_timeout_seconds = 300,
                                             .session_update_interval_seconds = 1800};

static dpif_app_config_t *default_app_config = &dpu_main_app_config;

static void dpif_app_config_init(int dpu_id, int sim) {
    if (dpu_id > 1) {
        if (sim) {
            default_app_config = &sim_n_app_config;
        } else {
            default_app_config = &dpu_n_app_config;
        }
    } else {
        if (sim) {
            default_app_config = &sim_main_app_config;
        } else {
            default_app_config = &dpu_main_app_config;
        }
    }
    dpif_config_init(dpu_id, sim);
}

int main(int argc, char **argv) {
    int ca, dpu_id = 1, is_sim = 0;
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    while ((ca = getopt(argc, argv, "hn:s:")) != -1) {
        switch (ca) {
        case 'n':
            dpu_id = atoi(optarg);
            break;
        case 's':
            is_sim = atoi(optarg);
            break;
        case 'h':
        default:
            printf("usage: %s -n <cpu_id> -s <0|1>\n", argv[0]);
            return 0;
        }
    }

    printf("DPIF Monitor Application starting...\n");

    dpi_device_t my_dpi_callbacks = {.dpi_session_create = monitor_session_create,
                                     .dpi_session_destroy = monitor_session_destroy,
                                     .dpi_session_analyze = monitor_session_analyze,
                                     .dpi_session_work = monitor_session_work,
                                     .dpi_session_update = monitor_session_update};

    if (dpi_register_device(&my_dpi_callbacks) < 0) {
        fprintf(stderr, "Error: Failed to register DPI callbacks with DPIF.\n");
        return -1;
    }
    printf("DPI callbacks registered.\n");

    dpif_app_config_init(dpu_id, is_sim);

    printf("Initializing DPIF library. \n");
    if (dpif_prepare(default_app_config) < 0) {
        fprintf(stderr, "Error: DPIF library prepare failed.\n");
        return -1;
    }
    if (dpif_start() < 0) {
        fprintf(stderr, "Error: DPIF library start failed.\n");
        return -1;
    }
    printf("DPIF library and EAL initialized successfully.\n");

    printf("DPIF testapp running. Press Ctrl+C to exit.\n");
    while (!force_quit) {
        sleep(1);
    }

    printf("\nShutting down DPIF testapp ...\n");

    dpif_stop();
    printf("DPIF stop signal sent.\n");

    dpif_cleanup();
    printf("DPIF resources and EAL cleaned up.\n");

    printf("DPIF testapp finished.\n");
    return 0;
}