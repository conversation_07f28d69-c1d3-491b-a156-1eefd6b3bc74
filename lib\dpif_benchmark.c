#include "dpif_private.h"

extern dpif_global_context_t *g_dpif_ctx;

/**
 * @brief Processes a packet based on the active benchmark mode.
 *
 * If a benchmark mode other than DPIF_BENCHMARK_MODE_DEFAULT is active,
 * this function handles the packet (e.g., echoes it, drops it) and returns 1.
 * Otherwise, it returns 0, indicating the packet should be processed normally by DPIF.
 * @param ctx The RX context.
 * @param m The mbuf containing the (potentially decapsulated) packet.
 * @return 1 if the packet was consumed by the benchmark module, 0 otherwise.
 */
int dpif_benchmark_process_packet(dpif_rx_context_t *ctx, struct rte_mbuf *m) {
    if (!g_dpif_ctx) {
        DPIF_LOG_CRITICAL("dpif_benchmark_process_packet_if_active: g_dpif_ctx is NULL. Benchmark functionality "
                          "disabled.");
        return 0;
    }

    if (!ctx) {
        DPIF_LOG_ERROR("dpif_benchmark_process_packet_if_active: RX context (ctx) is NULL.");
        if (m) {
            rte_pktmbuf_free(m);
        }
        return 1;
    }

    if (!m) {
        DPIF_LOG_ERROR("RX %u: dpif_benchmark_process_packet_if_active: Mbuf (m) is NULL.", ctx->lcore_id);
        rte_atomic64_inc(&ctx->dropped_pkts);
        return 1;
    }

    dp_metadata_t *meta = DPIF_MBUF_METADATA(m);

    if ((g_dpif_ctx->benchmark_mode == DPIF_BENCHMARK_MODE_ECHO ||
         g_dpif_ctx->benchmark_mode == DPIF_BENCHMARK_MODE_DELAY) &&
        !meta) {
        DPIF_LOG_ERROR("RX %u: Metadata (meta) is NULL, but required for active benchmark mode %d.",
                       ctx->lcore_id,
                       g_dpif_ctx->benchmark_mode);
        rte_pktmbuf_free(m);
        rte_atomic64_inc(&ctx->dropped_pkts);
        return 1;
    }

    switch (g_dpif_ctx->benchmark_mode) {
    case DPIF_BENCHMARK_MODE_ECHO: {
        const uint32_t DEFAULT_VERDICT = 1;  // NF_ACCEPT
        uint16_t queue_to_send_on = ctx->memif_queue_id;
        if (dpif_issue_verdict(meta, DEFAULT_VERDICT, queue_to_send_on, DPIF_MSG_SESSION_VERDICT) != 0) {
            DPIF_LOG_DEBUG("RX %u: Benchmark Bypass mode failed to send verdict for packet_id %u.",
                           ctx->lcore_id,
                           meta->priv_data.seqno);
            rte_pktmbuf_free(m);
            rte_atomic64_inc(&ctx->dropped_pkts);
        } else {
            DPIF_LOG_DEBUG(
                "RX %u: Benchmark Bypass mode sent verdict for packet_id %u.", ctx->lcore_id, meta->priv_data.seqno);
            rte_pktmbuf_free(m);
        }
        return 1;  // Signifies packet was consumed by benchmark module
    }
    case DPIF_BENCHMARK_MODE_DELAY: {
        DPIF_LOG_DEBUG("RX %u: Benchmark Drop mode dropping packet_id %u.", ctx->lcore_id, meta->priv_data.seqno);
        rte_pktmbuf_free(m);
        rte_atomic64_inc(&ctx->dropped_pkts);
        return 1;  // Signifies packet was consumed by benchmark module
    }
    default:
        // If mode is DPIF_BENCHMARK_MODE_NORMAL or any other undefined mode,
        // the packet will be processed normally by DPIF.
        break;
    }

    return 0;  // Signifies packet should be processed normally by DPIF
}

/**
 * @brief Sets the global DPIF benchmark operating mode.
 *
 * @param mode The benchmark mode to set.
 * @return 0 on success, negative errno on failure.
 */
int dpif_benchmark_set_mode(dpif_benchmark_mode_t mode) {
    if (!g_dpif_ctx) {
        DPIF_LOG_CRITICAL("dpif_benchmark_set_mode: g_dpif_ctx is NULL.");
        return -EFAULT;
    }
    if (mode < DPIF_BENCHMARK_MODE_ECHO || mode >= DPIF_BENCHMARK_MODE_DEFAULT) {
        DPIF_LOG_ERROR("dpif_benchmark_set_mode: Invalid mode %d.", mode);
        return -EINVAL;
    }
    g_dpif_ctx->benchmark_mode = mode;
    DPIF_LOG_INFO("Benchmark mode set to %d.", mode);
    return 0;
}

/**
 * @brief Gets the current global DPIF benchmark operating mode.
 *
 * @return The current dpif_benchmark_mode_t.
 */
dpif_benchmark_mode_t dpif_benchmark_get_mode(void) {
    if (!g_dpif_ctx) {
        DPIF_LOG_CRITICAL("dpif_benchmark_get_mode: g_dpif_ctx is NULL.");
        return DPIF_BENCHMARK_MODE_DEFAULT;  // Default to normal if context is missing
    }
    return g_dpif_ctx->benchmark_mode;
}
