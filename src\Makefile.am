#############################################################################
#                         _____       _ _                                   # 
#                        /  __ \     | (_)                                  # 
#                        | /  \/ __ _| |___  __                             #
#                        | |    / _` | | \ \/ /                             #
#                        | \__/\ (_| | | |>  <                              #
#                         \____/\__,_|_|_/_/\_\ inc.                        #
#                                                                           #
#############################################################################
#                                                                           #
#                       copyright 2025 by Calix, Inc.                       #
#                               Petaluma, CA                                #
#                                                                           #
#############################################################################
#
# Author: <PERSON> Li
#
# Purpose: Makefile.am for the libdpif sources
#
#############################################################################

bin_PROGRAMS = dpif_test dpif_cli

dpif_test_SOURCES = libdpif_main.c

dpif_test_CFLAGS = -Werror $(warning_mask) $(SANITIZER_FLAGS) -g $(COV_CCFLAGS_libdpif) -pthread -I$(top_srcdir)
dpif_test_LDADD = $(top_builddir)/lib/libdpif.la

dpif_cli_SOURCES = libdpif_cli.c
dpif_cli_CFLAGS = -g -O0 -Werror -Wall -Wuninitialized $(COV_CCFLAGS)  \
              -I${STAGING_DIR_HOST}/usr/include/libnl3  ${warning_mask}

# Code Coverage Support
if RM_OPT
override CFLAGS := $(shell echo $(CFLAGS) | sed "s@-O.@@g" )
endif
